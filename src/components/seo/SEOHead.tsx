'use client';

import Script from 'next/script';
import { SchemaMarkup } from '@/lib/seo/types';

interface SEOHeadProps {
  schemas?: SchemaMarkup[];
  additionalMeta?: React.ReactNode;
  preconnectUrls?: string[];
  prefetchUrls?: string[];
}

/**
 * SEO Head component for additional meta tags and schema markup
 */
export function SEOHead({ 
  schemas = [], 
  additionalMeta,
  preconnectUrls = [],
  prefetchUrls = []
}: SEOHeadProps) {
  return (
    <>
      {/* Preconnect to external domains */}
      {preconnectUrls.map((url, index) => (
        <link key={`preconnect-${index}`} rel="preconnect" href={url} />
      ))}
      
      {/* Prefetch important resources */}
      {prefetchUrls.map((url, index) => (
        <link key={`prefetch-${index}`} rel="prefetch" href={url} />
      ))}
      
      {/* Additional meta tags */}
      {additionalMeta}
      
      {/* Schema markup scripts */}
      {schemas.map((schema, index) => (
        <Script
          key={`schema-${index}`}
          id={`schema-${schema['@type'].toLowerCase()}-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema),
          }}
        />
      ))}
    </>
  );
}

/**
 * Breadcrumb component with schema markup
 */
interface BreadcrumbProps {
  items: Array<{ name: string; url: string }>;
  className?: string;
}

export function SEOBreadcrumb({ items, className = '' }: BreadcrumbProps) {
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return (
    <>
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      
      <nav className={`breadcrumb ${className}`} aria-label="Breadcrumb">
        <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
          {items.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-muted-foreground/50">/</span>
              )}
              {index === items.length - 1 ? (
                <span className="text-foreground font-medium">{item.name}</span>
              ) : (
                <a 
                  href={item.url} 
                  className="hover:text-foreground transition-colors"
                >
                  {item.name}
                </a>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

/**
 * FAQ component with schema markup
 */
interface FAQProps {
  faqs: Array<{ question: string; answer: string }>;
  className?: string;
}

export function SEOFAQ({ faqs, className = '' }: FAQProps) {
  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return (
    <>
      <Script
        id="faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
      
      <div className={`faq-section ${className}`}>
        {faqs.map((faq, index) => (
          <div key={index} className="faq-item mb-6">
            <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
            <p className="text-muted-foreground">{faq.answer}</p>
          </div>
        ))}
      </div>
    </>
  );
}

/**
 * Article component with schema markup
 */
interface ArticleProps {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  imageUrl?: string;
  content: string;
  tags?: string[];
  className?: string;
}

export function SEOArticle({ 
  title, 
  description, 
  author, 
  publishedTime, 
  modifiedTime, 
  imageUrl, 
  content, 
  tags = [],
  className = '' 
}: ArticleProps) {
  const articleSchema = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    image: imageUrl ? {
      '@type': 'ImageObject',
      url: imageUrl,
    } : undefined,
    author: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Bili Tool',
      logo: {
        '@type': 'ImageObject',
        url: 'https://bili-tool.com/images/logo.png',
      },
    },
    datePublished: publishedTime,
    dateModified: modifiedTime || publishedTime,
    keywords: tags.join(', '),
    wordCount: content.split(/\s+/).length,
  };

  return (
    <>
      <Script
        id="article-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema),
        }}
      />
      
      <article className={`article ${className}`}>
        <header className="article-header mb-6">
          <h1 className="text-3xl font-bold mb-2">{title}</h1>
          <p className="text-muted-foreground mb-4">{description}</p>
          <div className="article-meta text-sm text-muted-foreground">
            <span>By {author}</span>
            <span className="mx-2">•</span>
            <time dateTime={publishedTime}>
              {new Date(publishedTime).toLocaleDateString()}
            </time>
            {modifiedTime && modifiedTime !== publishedTime && (
              <>
                <span className="mx-2">•</span>
                <span>Updated {new Date(modifiedTime).toLocaleDateString()}</span>
              </>
            )}
          </div>
        </header>
        
        <div className="article-content prose max-w-none">
          {content}
        </div>
        
        {tags.length > 0 && (
          <footer className="article-footer mt-6">
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 bg-accent text-accent-foreground rounded-md text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
          </footer>
        )}
      </article>
    </>
  );
}

/**
 * Product component with schema markup (for premium tools)
 */
interface ProductProps {
  name: string;
  description: string;
  price: number;
  currency?: string;
  imageUrl?: string;
  rating?: number;
  reviewCount?: number;
  className?: string;
}

export function SEOProduct({ 
  name, 
  description, 
  price, 
  currency = 'USD', 
  imageUrl, 
  rating = 4.8, 
  reviewCount = 150,
  className = '' 
}: ProductProps) {
  const productSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    image: imageUrl,
    brand: {
      '@type': 'Brand',
      name: 'Bili Tool',
    },
    offers: {
      '@type': 'Offer',
      price,
      priceCurrency: currency,
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: rating,
      reviewCount,
      bestRating: 5,
      worstRating: 1,
    },
  };

  return (
    <>
      <Script
        id="product-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(productSchema),
        }}
      />
      
      <div className={`product ${className}`}>
        {imageUrl && (
          <img 
            src={imageUrl} 
            alt={name}
            className="product-image w-full h-48 object-cover rounded-lg mb-4"
          />
        )}
        <h2 className="product-name text-xl font-semibold mb-2">{name}</h2>
        <p className="product-description text-muted-foreground mb-4">{description}</p>
        <div className="product-price text-2xl font-bold text-primary">
          {currency === 'USD' ? '$' : currency}{price}
        </div>
        {rating && reviewCount && (
          <div className="product-rating mt-2 text-sm text-muted-foreground">
            ⭐ {rating}/5 ({reviewCount} reviews)
          </div>
        )}
      </div>
    </>
  );
}
