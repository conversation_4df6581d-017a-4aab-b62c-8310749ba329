'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>dingUp, 
  AlertTriangle, 
  CheckCircle, 
  Search, 
  Globe, 
  Smartphone, 
  Zap,
  BarChart3,
  FileText,
  Link,
  Image
} from 'lucide-react';
import { SEOAnalysis, SEOIssue, SEORecommendation } from '@/lib/seo/types';
import { getSEOGrade } from '@/lib/seo/scoring-system';

interface SEODashboardProps {
  analysis?: SEOAnalysis;
  loading?: boolean;
  onAnalyze?: (url: string) => void;
}

export function SEODashboard({ analysis, loading, onAnalyze }: SEODashboardProps) {
  const [url, setUrl] = useState('');
  const grade = analysis ? getSEOGrade(analysis.score) : null;

  const handleAnalyze = () => {
    if (url && onAnalyze) {
      onAnalyze(url);
    }
  };

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">SEO Analysis</h2>
        <div className="flex gap-4">
          <input
            type="url"
            placeholder="Enter URL to analyze..."
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="flex-1 h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <button
            onClick={handleAnalyze}
            disabled={!url || loading}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                Analyzing...
              </>
            ) : (
              <>
                <Search size={16} />
                Analyze
              </>
            )}
          </button>
        </div>
      </div>

      {analysis && (
        <>
          {/* Score Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">SEO Score</p>
                  <p className="text-3xl font-bold" style={{ color: grade?.color }}>
                    {analysis.score}
                  </p>
                  <p className="text-sm font-medium" style={{ color: grade?.color }}>
                    Grade {grade?.grade}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-primary/10">
                  <TrendingUp className="h-6 w-6 text-primary" />
                </div>
              </div>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Issues Found</p>
                  <p className="text-3xl font-bold text-red-600">
                    {analysis.issues.length}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analysis.issues.filter(i => i.impact === 'high').length} critical
                  </p>
                </div>
                <div className="p-3 rounded-full bg-red-100">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Recommendations</p>
                  <p className="text-3xl font-bold text-blue-600">
                    {analysis.recommendations.length}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analysis.recommendations.filter(r => r.priority === 'high').length} high priority
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Page Size</p>
                  <p className="text-3xl font-bold">
                    {(analysis.metrics.pageSize / 1024).toFixed(0)}
                  </p>
                  <p className="text-sm text-muted-foreground">KB</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Title Length"
              value={analysis.metrics.titleLength}
              unit="chars"
              icon={<FileText className="h-5 w-5" />}
              status={
                analysis.metrics.titleLength >= 30 && analysis.metrics.titleLength <= 60
                  ? 'good'
                  : 'warning'
              }
            />
            <MetricCard
              title="Description Length"
              value={analysis.metrics.descriptionLength}
              unit="chars"
              icon={<FileText className="h-5 w-5" />}
              status={
                analysis.metrics.descriptionLength >= 120 && analysis.metrics.descriptionLength <= 160
                  ? 'good'
                  : 'warning'
              }
            />
            <MetricCard
              title="Internal Links"
              value={analysis.metrics.internalLinks}
              unit="links"
              icon={<Link className="h-5 w-5" />}
              status={analysis.metrics.internalLinks >= 3 ? 'good' : 'warning'}
            />
            <MetricCard
              title="Images"
              value={analysis.metrics.imageOptimization.totalImages}
              unit="total"
              icon={<Image className="h-5 w-5" />}
              status={
                analysis.metrics.imageOptimization.imagesWithoutAlt === 0 ? 'good' : 'warning'
              }
            />
          </div>

          {/* Issues and Recommendations */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <IssuesPanel issues={analysis.issues} />
            <RecommendationsPanel recommendations={analysis.recommendations} />
          </div>

          {/* Keyword Density */}
          {Object.keys(analysis.metrics.keywordDensity).length > 0 && (
            <KeywordDensityPanel keywordDensity={analysis.metrics.keywordDensity} />
          )}
        </>
      )}
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: number;
  unit: string;
  icon: React.ReactNode;
  status: 'good' | 'warning' | 'error';
}

function MetricCard({ title, value, unit, icon, status }: MetricCardProps) {
  const statusColors = {
    good: 'text-green-600 bg-green-100',
    warning: 'text-yellow-600 bg-yellow-100',
    error: 'text-red-600 bg-red-100',
  };

  return (
    <div className="bg-card border border-border rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${statusColors[status]}`}>
          {icon}
        </div>
        <div className={`w-3 h-3 rounded-full ${
          status === 'good' ? 'bg-green-500' :
          status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
        }`} />
      </div>
      <p className="text-sm text-muted-foreground">{title}</p>
      <p className="text-2xl font-bold">
        {value} <span className="text-sm font-normal text-muted-foreground">{unit}</span>
      </p>
    </div>
  );
}

interface IssuesPanelProps {
  issues: SEOIssue[];
}

function IssuesPanel({ issues }: IssuesPanelProps) {
  const criticalIssues = issues.filter(issue => issue.impact === 'high');
  const warningIssues = issues.filter(issue => issue.impact === 'medium');
  const infoIssues = issues.filter(issue => issue.impact === 'low');

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <AlertTriangle className="h-5 w-5 text-red-500" />
        Issues ({issues.length})
      </h3>
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {criticalIssues.map((issue, index) => (
          <IssueItem key={`critical-${index}`} issue={issue} />
        ))}
        {warningIssues.map((issue, index) => (
          <IssueItem key={`warning-${index}`} issue={issue} />
        ))}
        {infoIssues.map((issue, index) => (
          <IssueItem key={`info-${index}`} issue={issue} />
        ))}
      </div>
    </div>
  );
}

interface RecommendationsPanelProps {
  recommendations: SEORecommendation[];
}

function RecommendationsPanel({ recommendations }: RecommendationsPanelProps) {
  const highPriority = recommendations.filter(rec => rec.priority === 'high');
  const mediumPriority = recommendations.filter(rec => rec.priority === 'medium');
  const lowPriority = recommendations.filter(rec => rec.priority === 'low');

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <CheckCircle className="h-5 w-5 text-green-500" />
        Recommendations ({recommendations.length})
      </h3>
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {highPriority.map((rec, index) => (
          <RecommendationItem key={`high-${index}`} recommendation={rec} />
        ))}
        {mediumPriority.map((rec, index) => (
          <RecommendationItem key={`medium-${index}`} recommendation={rec} />
        ))}
        {lowPriority.map((rec, index) => (
          <RecommendationItem key={`low-${index}`} recommendation={rec} />
        ))}
      </div>
    </div>
  );
}

function IssueItem({ issue }: { issue: SEOIssue }) {
  const impactColors = {
    high: 'border-red-500 bg-red-50',
    medium: 'border-yellow-500 bg-yellow-50',
    low: 'border-blue-500 bg-blue-50',
  };

  return (
    <div className={`border-l-4 pl-4 py-2 ${impactColors[issue.impact]}`}>
      <div className="flex items-center gap-2 mb-1">
        <span className="font-medium text-sm">{issue.title}</span>
        <span className={`px-2 py-1 rounded text-xs ${
          issue.impact === 'high' ? 'bg-red-100 text-red-800' :
          issue.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {issue.impact}
        </span>
      </div>
      <p className="text-xs text-muted-foreground">{issue.description}</p>
      {issue.fix && (
        <p className="text-xs text-green-600 mt-1">Fix: {issue.fix}</p>
      )}
    </div>
  );
}

function RecommendationItem({ recommendation }: { recommendation: SEORecommendation }) {
  const priorityColors = {
    high: 'border-red-500 bg-red-50',
    medium: 'border-yellow-500 bg-yellow-50',
    low: 'border-green-500 bg-green-50',
  };

  return (
    <div className={`border-l-4 pl-4 py-2 ${priorityColors[recommendation.priority]}`}>
      <div className="flex items-center gap-2 mb-1">
        <span className="font-medium text-sm">{recommendation.title}</span>
        <span className={`px-2 py-1 rounded text-xs ${
          recommendation.priority === 'high' ? 'bg-red-100 text-red-800' :
          recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          {recommendation.priority}
        </span>
      </div>
      <p className="text-xs text-muted-foreground">{recommendation.description}</p>
      <p className="text-xs text-blue-600 mt-1">Action: {recommendation.action}</p>
    </div>
  );
}

interface KeywordDensityPanelProps {
  keywordDensity: Record<string, number>;
}

function KeywordDensityPanel({ keywordDensity }: KeywordDensityPanelProps) {
  const sortedKeywords = Object.entries(keywordDensity)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10);

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <BarChart3 className="h-5 w-5 text-blue-500" />
        Top Keywords
      </h3>
      <div className="space-y-2">
        {sortedKeywords.map(([keyword, density]) => (
          <div key={keyword} className="flex items-center justify-between">
            <span className="text-sm font-medium">{keyword}</span>
            <div className="flex items-center gap-2">
              <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 rounded-full"
                  style={{ width: `${Math.min(density * 10, 100)}%` }}
                />
              </div>
              <span className="text-xs text-muted-foreground w-12 text-right">
                {density.toFixed(1)}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
