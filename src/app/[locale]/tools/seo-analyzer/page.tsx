'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { performSEOAnalysis, getSEOGrade } from '@/lib/seo/scoring-system';
import { SEOAnalysis } from '@/lib/seo/types';

export default function SEOAnalyzerPage() {
  const locale = useLocale();
  const t = useTranslations('tools.seoAnalyzer');
  const [url, setUrl] = useState('');
  const [content, setContent] = useState('');
  const [targetKeywords, setTargetKeywords] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<SEOAnalysis | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url || !content) {
      setError(locale === 'zh' ? '请输入URL和内容' : 'Please enter URL and content');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Extract basic information from content
      const title = extractTitle(content);
      const description = extractDescription(content);
      const headings = extractHeadings(content);
      const images = extractImages(content);
      const links = extractLinks(content);
      const keywords = targetKeywords.split(',').map(k => k.trim()).filter(Boolean);
      
      // Perform SEO analysis
      const analysis = performSEOAnalysis(
        url,
        title,
        description,
        content,
        headings,
        images,
        links,
        keywords
      );
      
      setResult(analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setLoading(false);
    }
  };

  const extractTitle = (html: string): string => {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1] : '';
  };

  const extractDescription = (html: string): string => {
    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["'][^>]*>/i);
    return descMatch ? descMatch[1] : '';
  };

  const extractHeadings = (html: string): { level: number; text: string }[] => {
    const headingRegex = /<h([1-6])[^>]*>([^<]+)<\/h[1-6]>/gi;
    const headings: { level: number; text: string }[] = [];
    let match;
    
    while ((match = headingRegex.exec(html)) !== null) {
      headings.push({
        level: parseInt(match[1]),
        text: match[2].trim(),
      });
    }
    
    return headings;
  };

  const extractImages = (html: string): { src: string; alt?: string }[] => {
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*>/gi;
    const images: { src: string; alt?: string }[] = [];
    let match;
    
    while ((match = imgRegex.exec(html)) !== null) {
      images.push({
        src: match[1],
        alt: match[2] || undefined,
      });
    }
    
    return images;
  };

  const extractLinks = (html: string): { href: string; text: string; internal: boolean }[] => {
    const linkRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
    const links: { href: string; text: string; internal: boolean }[] = [];
    let match;
    
    while ((match = linkRegex.exec(html)) !== null) {
      const href = match[1];
      const isInternal = href.startsWith('/') || href.includes(new URL(url).hostname);
      
      links.push({
        href,
        text: match[2].trim(),
        internal: isInternal,
      });
    }
    
    return links;
  };

  const downloadReport = () => {
    if (result) {
      const report = {
        url,
        score: result.score,
        grade: getSEOGrade(result.score),
        issues: result.issues,
        recommendations: result.recommendations,
        metrics: result.metrics,
        analyzedAt: new Date().toISOString(),
      };
      
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = `seo-analysis-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(downloadUrl);
    }
  };

  const copyReport = () => {
    if (result) {
      const report = `SEO Analysis Report for ${url}\n\nScore: ${result.score}/100\nGrade: ${getSEOGrade(result.score).grade}\n\nIssues Found: ${result.issues.length}\nRecommendations: ${result.recommendations.length}\n\nGenerated on: ${new Date().toLocaleString()}`;
      navigator.clipboard.writeText(report);
    }
  };

  const grade = result ? getSEOGrade(result.score) : null;

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* Back button */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>{locale === 'zh' ? '返回' : 'Back'}</span>
        </button>

        {/* Tool header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">
            {locale === 'zh' ? 'SEO分析工具' : 'SEO Analyzer Tool'}
          </h1>
          <p className="text-muted-foreground mt-2">
            {locale === 'zh' 
              ? '分析网页的SEO优化情况，获取详细的优化建议和评分'
              : 'Analyze webpage SEO optimization, get detailed recommendations and scoring'
            }
          </p>
        </div>

        {/* Analysis form */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="url" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '网页URL' : 'Website URL'}
              </label>
              <input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>
            
            <div>
              <label htmlFor="content" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '网页HTML内容' : 'Webpage HTML Content'}
              </label>
              <textarea
                id="content"
                placeholder={locale === 'zh' ? '粘贴网页的HTML源代码...' : 'Paste the HTML source code of the webpage...'}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-32 px-4 py-2 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-vertical"
                required
              />
            </div>
            
            <div>
              <label htmlFor="keywords" className="block text-sm font-medium mb-2">
                {locale === 'zh' ? '目标关键词（可选）' : 'Target Keywords (Optional)'}
              </label>
              <input
                id="keywords"
                type="text"
                placeholder={locale === 'zh' ? '关键词1, 关键词2, 关键词3' : 'keyword1, keyword2, keyword3'}
                value={targetKeywords}
                onChange={(e) => setTargetKeywords(e.target.value)}
                className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            
            {error && <p className="text-red-500 text-sm">{error}</p>}
            
            <button
              type="submit"
              disabled={loading}
              className="w-full h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center justify-center gap-2 disabled:opacity-70"
            >
              {loading ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  <span>{locale === 'zh' ? '分析中...' : 'Analyzing...'}</span>
                </>
              ) : (
                <>
                  <Search size={16} />
                  <span>{locale === 'zh' ? '开始分析' : 'Start Analysis'}</span>
                </>
              )}
            </button>
          </form>
        </div>

        {/* Results */}
        {result && (
          <div className="space-y-6">
            {/* Score overview */}
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">
                  {locale === 'zh' ? 'SEO评分' : 'SEO Score'}
                </h3>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={copyReport}
                    className="p-2 rounded hover:bg-accent"
                    title={locale === 'zh' ? '复制报告' : 'Copy Report'}
                  >
                    <Copy size={16} />
                  </button>
                  <button 
                    onClick={downloadReport}
                    className="p-2 rounded hover:bg-accent"
                    title={locale === 'zh' ? '下载报告' : 'Download Report'}
                  >
                    <Download size={16} />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="text-4xl font-bold" style={{ color: grade?.color }}>
                  {result.score}/100
                </div>
                <div>
                  <div className="text-2xl font-semibold" style={{ color: grade?.color }}>
                    {grade?.grade}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {grade?.description}
                  </div>
                </div>
              </div>
            </div>

            {/* Issues */}
            {result.issues.length > 0 && (
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <AlertTriangle size={20} className="text-orange-500" />
                  {locale === 'zh' ? '发现的问题' : 'Issues Found'} ({result.issues.length})
                </h3>
                <div className="space-y-3">
                  {result.issues.map((issue, index) => (
                    <div key={index} className="border-l-4 border-orange-500 pl-4">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{issue.title}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          issue.impact === 'high' ? 'bg-red-100 text-red-800' :
                          issue.impact === 'medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {issue.impact}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{issue.description}</p>
                      {issue.fix && (
                        <p className="text-sm text-blue-600">{locale === 'zh' ? '解决方案：' : 'Fix: '}{issue.fix}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {result.recommendations.length > 0 && (
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <TrendingUp size={20} className="text-green-500" />
                  {locale === 'zh' ? '优化建议' : 'Recommendations'} ({result.recommendations.length})
                </h3>
                <div className="space-y-3">
                  {result.recommendations.map((rec, index) => (
                    <div key={index} className="border-l-4 border-green-500 pl-4">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{rec.title}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                          rec.priority === 'medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {rec.priority}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{rec.description}</p>
                      <p className="text-sm text-green-600">{locale === 'zh' ? '行动：' : 'Action: '}{rec.action}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Metrics */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">
                {locale === 'zh' ? '详细指标' : 'Detailed Metrics'}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.titleLength}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '标题长度' : 'Title Length'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.descriptionLength}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '描述长度' : 'Description Length'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.headingStructure.h1Count}</div>
                  <div className="text-sm text-muted-foreground">H1 {locale === 'zh' ? '标签' : 'Tags'}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.metrics.imageOptimization.totalImages}</div>
                  <div className="text-sm text-muted-foreground">
                    {locale === 'zh' ? '图片数量' : 'Images'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
