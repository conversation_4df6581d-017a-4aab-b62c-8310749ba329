import { Tool, Notification, SubMenuItem, MenuItem, MenuCategory } from './types';
import { tools, sidebarMenu } from './data';
import { v4 as uuidv4 } from 'uuid';

/**
 * 按分类组织工具
 */
export function getToolsByCategory(): Record<string, Tool[]> {
  return tools.reduce((acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);
}

/**
 * 按子分类组织工具
 */
export function getToolsBySubCategory(category: string): Record<string, Tool[]> {
  const categoryTools = tools.filter(tool => tool.category === category);
  
  return categoryTools.reduce((acc, tool) => {
    const subCategory = tool.subCategory || '未分类';
    if (!acc[subCategory]) {
      acc[subCategory] = [];
    }
    acc[subCategory].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);
}

/**
 * 获取特定子分类的工具
 */
export function getToolsForSubCategory(category: string, subCategory: string): Tool[] {
  return tools.filter(tool => 
    tool.category === category && 
    tool.subCategory === subCategory
  );
}

/**
 * 通过子分类ID获取工具
 */
export function getToolsBySubCategoryId(category: string, subCategoryId: string): Tool[] {
  // 先找到对应的分类
  const menuCategory = sidebarMenu.find(cat => cat.category === category);
  if (!menuCategory || !menuCategory.subItems || !menuCategory.subItems[subCategoryId]) {
    return [];
  }

  // 获取该子分类下的所有工具ID
  const subCategoryItemIds = menuCategory.subItems[subCategoryId].map(item => item.id);
  
  // 获取分类下所有ID在列表中的工具
  return tools.filter(tool => 
    tool.category === category && 
    subCategoryItemIds.includes(tool.id)
  );
}

/**
 * 获取一级分类下的子菜单项
 */
export function getSubMenuItems(categoryName: string): SubMenuItem[] | undefined {
  const category = sidebarMenu.find(cat => cat.category === categoryName);
  if (category && category.subItems) {
    // 如果参数是分类名称，返回所有子分类下的菜单项的合并数组
    const allSubItems: SubMenuItem[] = [];
    Object.values(category.subItems).forEach(subItems => {
      allSubItems.push(...subItems);
    });
    return allSubItems;
  }
  return undefined;
}

/**
 * 搜索工具
 */
export function searchTools(query: string): Tool[] {
  if (!query) return tools;
  
  const lowerQuery = query.toLowerCase();
  return tools.filter(
    tool =>
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery)
  );
}

/**
 * 按分类组织筛选后的工具
 */
export function getFilteredToolsByCategory(query: string): Record<string, Tool[]> {
  const filteredTools = searchTools(query);
  return filteredTools.reduce((acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);
}

/**
 * 获取收藏的工具
 */
export function getFavoriteTools(): Tool[] {
  return tools.filter(tool => tool.isFavorite);
}

/**
 * 切换工具收藏状态
 */
export function toggleToolFavorite(toolId: string): Tool | undefined {
  const tool = tools.find(t => t.id === toolId);
  if (tool) {
    tool.isFavorite = !tool.isFavorite;
  }
  return tool;
}

/**
 * 获取工具详情
 */
export function getToolById(toolId: string): Tool | undefined {
  return tools.find(tool => tool.id === toolId);
}

/**
 * 检查工具是否已开发
 */
export function isToolDeveloped(toolId: string): boolean {
  const developedTools = [
    'whois',
    'ip-location',
    'color-extract',
    'dns-lookup',
    'img-compress',
    'img-watermark',
    'img-resize',
    'json-formatter',
    'code-compress',
    'regex-test',
    'random-generator',
    'text-diff',
    'word-count',
    'case-converter',
    'base64-encode',
    'json-format',
    'base64',
    'md5',
    'sha256',
    'url-encode',
    'color-converter',
    'random-ip',
    'qrcode',
    'jwt-decoder',
    'json-to-yaml',
    'yaml-to-json',
    'yaml-formatter',
    'yaml-to-properties',
    'json-to-csv',
    'csv-to-json',
    'sha-calculator',
    'md5-collision',
    'aes-encryption',
    'des-encryption',
    'seo-analyzer'
  ];
  return developedTools.includes(toolId);
}

/**
 * 创建通知对象
 */
export function createNotification(
  title: string,
  message: string,
  type: 'success' | 'error' | 'info' = 'info'
): Notification {
  return {
    id: uuidv4(),
    title,
    message,
    type
  };
}

export function createTool(name: string, category: string, description: string = '', icon: string = '🔧'): Tool {
  return {
    id: uuidv4(),
    name,
    category,
    description,
    icon,
    isFavorite: false
  };
}

/**
 * 根据当前语言环境获取工具名称
 * @param tool 工具对象
 * @param locale 当前语言环境 ('zh' | 'en')
 * @returns 工具名称
 */
export function getLocalizedToolName(tool: Tool, locale: string): string {
  if (locale === 'en' && tool.en_name) {
    return tool.en_name;
  }
  if (locale === 'zh' && tool.zh_name) {
    return tool.zh_name;
  }
  return tool.name; // 返回默认名称作为回退
}

/**
 * 根据当前语言环境获取工具描述
 * @param tool 工具对象
 * @param locale 当前语言环境 ('zh' | 'en')
 * @returns 工具描述
 */
export function getLocalizedToolDescription(tool: Tool, locale: string): string {
  if (locale === 'en' && tool.en_description) {
    return tool.en_description;
  }
  if (locale === 'zh' && tool.zh_description) {
    return tool.zh_description;
  }
  return tool.description; // 返回默认描述作为回退
}

/**
 * 获取当前路径中的语言环境
 * @param pathname 当前路径
 * @returns 语言环境 ('zh' | 'en')
 */
export function getLocaleFromPathname(pathname: string): string {
  const parts = pathname.split('/').filter(Boolean);
  if (parts.length > 0 && ['zh', 'en'].includes(parts[0])) {
    return parts[0];
  }
  return 'zh'; // 默认返回中文
}

/**
 * 根据当前语言环境获取菜单分类名称
 * @param category 菜单分类对象
 * @param locale 当前语言环境 ('zh' | 'en')
 * @returns 菜单分类名称
 */
export function getLocalizedCategoryName(category: MenuCategory, locale: string): string {
  if (locale === 'en' && category.en_category) {
    return category.en_category;
  }
  if (locale === 'zh' && category.zh_category) {
    return category.zh_category;
  }
  return category.category; // 返回默认名称作为回退
}

/**
 * 根据当前语言环境获取菜单项名称
 * @param item 菜单项对象
 * @param locale 当前语言环境 ('zh' | 'en')
 * @returns 菜单项名称
 */
export function getLocalizedMenuItemName(item: MenuItem | SubMenuItem, locale: string): string {
  if (locale === 'en' && item.en_name) {
    return item.en_name;
  }
  if (locale === 'zh' && item.zh_name) {
    return item.zh_name;
  }
  return item.name; // 返回默认名称作为回退
} 