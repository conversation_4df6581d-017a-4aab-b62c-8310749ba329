import { 
  SEOAnalysis, 
  SEOIssue, 
  SEORecommendation, 
  SEOMetrics,
  PerformanceMetrics,
  MobileOptimization 
} from './types';
import { SEO_LIMITS, SEO_SCORING_WEIGHTS, PERFORMANCE_THRESHOLDS } from './config';
import { analyzePage, findSEOIssues, generateRecommendations } from './analysis';
import { analyzeContentSEO } from './keyword-tools';

/**
 * Comprehensive SEO scoring and analysis system
 */

/**
 * Perform complete SEO analysis
 */
export function performSEOAnalysis(
  url: string,
  title: string,
  description: string,
  content: string,
  headings: { level: number; text: string }[],
  images: { src: string; alt?: string; size?: number }[],
  links: { href: string; text: string; internal: boolean }[],
  targetKeywords: string[] = [],
  performanceMetrics?: PerformanceMetrics,
  mobileOptimization?: MobileOptimization
): SEOAnalysis {
  // Basic page analysis
  const pageAnalysis = analyzePage(title, description, content, url, headings, images, links);
  
  // Content analysis
  const contentAnalysis = analyzeContentSEO(title, description, content, targetKeywords);
  
  // Technical SEO analysis
  const technicalIssues = analyzeTechnicalSEO(url, title, description, headings, images);
  
  // Performance analysis
  const performanceIssues = performanceMetrics 
    ? analyzePerformance(performanceMetrics)
    : [];
  
  // Mobile optimization analysis
  const mobileIssues = mobileOptimization 
    ? analyzeMobileOptimization(mobileOptimization)
    : [];
  
  // Combine all issues
  const allIssues = [
    ...pageAnalysis.issues,
    ...technicalIssues,
    ...performanceIssues,
    ...mobileIssues,
  ];
  
  // Generate comprehensive recommendations
  const recommendations = generateComprehensiveRecommendations(
    allIssues,
    pageAnalysis.metrics,
    contentAnalysis,
    targetKeywords
  );
  
  // Calculate final score
  const score = calculateComprehensiveScore(
    pageAnalysis.metrics,
    allIssues,
    contentAnalysis,
    performanceMetrics,
    mobileOptimization
  );
  
  return {
    score,
    issues: allIssues,
    recommendations,
    metrics: pageAnalysis.metrics,
  };
}

/**
 * Analyze technical SEO aspects
 */
export function analyzeTechnicalSEO(
  url: string,
  title: string,
  description: string,
  headings: { level: number; text: string }[],
  images: { src: string; alt?: string; size?: number }[]
): SEOIssue[] {
  const issues: SEOIssue[] = [];
  
  // URL structure analysis
  if (url.length > SEO_LIMITS.URL_MAX_LENGTH) {
    issues.push({
      type: 'warning',
      category: 'technical',
      title: 'URL Too Long',
      description: `URL length is ${url.length} characters, should be under ${SEO_LIMITS.URL_MAX_LENGTH}`,
      impact: 'medium',
      fix: 'Shorten URL structure and remove unnecessary parameters',
    });
  }
  
  if (url.includes('_')) {
    issues.push({
      type: 'warning',
      category: 'technical',
      title: 'URL Contains Underscores',
      description: 'URLs should use hyphens instead of underscores for better SEO',
      impact: 'low',
      fix: 'Replace underscores with hyphens in URL structure',
    });
  }
  
  if (url.match(/[A-Z]/)) {
    issues.push({
      type: 'warning',
      category: 'technical',
      title: 'URL Contains Uppercase Letters',
      description: 'URLs should be lowercase for consistency',
      impact: 'low',
      fix: 'Convert URL to lowercase',
    });
  }
  
  // Title analysis
  if (!title) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing Title Tag',
      description: 'Page is missing a title tag',
      impact: 'high',
      fix: 'Add a descriptive title tag',
    });
  } else {
    if (title.length < SEO_LIMITS.TITLE_MIN_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Title Too Short',
        description: `Title is ${title.length} characters, should be at least ${SEO_LIMITS.TITLE_MIN_LENGTH}`,
        impact: 'medium',
        fix: 'Expand title with more descriptive keywords',
      });
    }
    
    if (title.length > SEO_LIMITS.TITLE_MAX_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Title Too Long',
        description: `Title is ${title.length} characters, should be at most ${SEO_LIMITS.TITLE_MAX_LENGTH}`,
        impact: 'medium',
        fix: 'Shorten title while keeping key information',
      });
    }
  }
  
  // Meta description analysis
  if (!description) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing Meta Description',
      description: 'Page is missing a meta description',
      impact: 'high',
      fix: 'Add a compelling meta description',
    });
  } else {
    if (description.length < SEO_LIMITS.DESCRIPTION_MIN_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Meta Description Too Short',
        description: `Description is ${description.length} characters, should be at least ${SEO_LIMITS.DESCRIPTION_MIN_LENGTH}`,
        impact: 'medium',
        fix: 'Expand description with more details',
      });
    }
    
    if (description.length > SEO_LIMITS.DESCRIPTION_MAX_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Meta Description Too Long',
        description: `Description is ${description.length} characters, should be at most ${SEO_LIMITS.DESCRIPTION_MAX_LENGTH}`,
        impact: 'medium',
        fix: 'Shorten description while keeping key information',
      });
    }
  }
  
  // Heading structure analysis
  const h1Count = headings.filter(h => h.level === 1).length;
  if (h1Count === 0) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing H1 Tag',
      description: 'Page is missing an H1 heading',
      impact: 'high',
      fix: 'Add a descriptive H1 heading',
    });
  } else if (h1Count > 1) {
    issues.push({
      type: 'warning',
      category: 'content',
      title: 'Multiple H1 Tags',
      description: `Page has ${h1Count} H1 tags, should have only one`,
      impact: 'medium',
      fix: 'Use only one H1 tag per page',
    });
  }
  
  // Check heading hierarchy
  const headingLevels = headings.map(h => h.level);
  for (let i = 1; i < headingLevels.length; i++) {
    if (headingLevels[i] > headingLevels[i - 1] + 1) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Broken Heading Hierarchy',
        description: 'Heading levels should not skip (e.g., H1 to H3 without H2)',
        impact: 'low',
        fix: 'Maintain proper heading hierarchy (H1 → H2 → H3, etc.)',
      });
      break;
    }
  }
  
  // Image optimization analysis
  const imagesWithoutAlt = images.filter(img => !img.alt || img.alt.trim().length === 0);
  if (imagesWithoutAlt.length > 0) {
    issues.push({
      type: 'warning',
      category: 'accessibility',
      title: 'Images Missing Alt Text',
      description: `${imagesWithoutAlt.length} images are missing alt text`,
      impact: 'medium',
      fix: 'Add descriptive alt text to all images',
    });
  }
  
  const largeImages = images.filter(img => img.size && img.size > 500000); // > 500KB
  if (largeImages.length > 0) {
    issues.push({
      type: 'warning',
      category: 'performance',
      title: 'Large Images Detected',
      description: `${largeImages.length} images are larger than 500KB`,
      impact: 'medium',
      fix: 'Compress images and use modern formats like WebP',
    });
  }
  
  return issues;
}

/**
 * Analyze performance metrics
 */
export function analyzePerformance(metrics: PerformanceMetrics): SEOIssue[] {
  const issues: SEOIssue[] = [];
  
  if (metrics.firstContentfulPaint > PERFORMANCE_THRESHOLDS.GOOD_FCP) {
    issues.push({
      type: 'warning',
      category: 'performance',
      title: 'Slow First Contentful Paint',
      description: `FCP is ${metrics.firstContentfulPaint}ms, should be under ${PERFORMANCE_THRESHOLDS.GOOD_FCP}ms`,
      impact: 'high',
      fix: 'Optimize critical rendering path and reduce server response time',
    });
  }
  
  if (metrics.largestContentfulPaint > PERFORMANCE_THRESHOLDS.GOOD_LCP) {
    issues.push({
      type: 'warning',
      category: 'performance',
      title: 'Slow Largest Contentful Paint',
      description: `LCP is ${metrics.largestContentfulPaint}ms, should be under ${PERFORMANCE_THRESHOLDS.GOOD_LCP}ms`,
      impact: 'high',
      fix: 'Optimize largest content element and improve server response time',
    });
  }
  
  if (metrics.cumulativeLayoutShift > PERFORMANCE_THRESHOLDS.GOOD_CLS) {
    issues.push({
      type: 'warning',
      category: 'performance',
      title: 'High Cumulative Layout Shift',
      description: `CLS is ${metrics.cumulativeLayoutShift}, should be under ${PERFORMANCE_THRESHOLDS.GOOD_CLS}`,
      impact: 'medium',
      fix: 'Set explicit dimensions for images and avoid inserting content above existing content',
    });
  }
  
  if (metrics.firstInputDelay > PERFORMANCE_THRESHOLDS.GOOD_FID) {
    issues.push({
      type: 'warning',
      category: 'performance',
      title: 'High First Input Delay',
      description: `FID is ${metrics.firstInputDelay}ms, should be under ${PERFORMANCE_THRESHOLDS.GOOD_FID}ms`,
      impact: 'medium',
      fix: 'Reduce JavaScript execution time and break up long tasks',
    });
  }
  
  return issues;
}

/**
 * Analyze mobile optimization
 */
export function analyzeMobileOptimization(mobile: MobileOptimization): SEOIssue[] {
  const issues: SEOIssue[] = [];
  
  if (!mobile.isResponsive) {
    issues.push({
      type: 'error',
      category: 'technical',
      title: 'Not Mobile Responsive',
      description: 'Page is not responsive on mobile devices',
      impact: 'high',
      fix: 'Implement responsive design with flexible layouts',
    });
  }
  
  if (!mobile.viewportConfigured) {
    issues.push({
      type: 'error',
      category: 'technical',
      title: 'Missing Viewport Meta Tag',
      description: 'Page is missing viewport meta tag for mobile optimization',
      impact: 'high',
      fix: 'Add viewport meta tag: <meta name="viewport" content="width=device-width, initial-scale=1">',
    });
  }
  
  if (!mobile.touchTargetsOptimal) {
    issues.push({
      type: 'warning',
      category: 'accessibility',
      title: 'Touch Targets Too Small',
      description: 'Touch targets are too small for mobile users',
      impact: 'medium',
      fix: 'Ensure touch targets are at least 44px in size',
    });
  }
  
  if (!mobile.fontSizeReadable) {
    issues.push({
      type: 'warning',
      category: 'accessibility',
      title: 'Font Size Too Small',
      description: 'Font size is too small for mobile reading',
      impact: 'medium',
      fix: 'Use minimum 16px font size for body text on mobile',
    });
  }
  
  if (!mobile.contentFitsViewport) {
    issues.push({
      type: 'warning',
      category: 'technical',
      title: 'Content Overflows Viewport',
      description: 'Content extends beyond viewport width on mobile',
      impact: 'medium',
      fix: 'Ensure content fits within viewport width',
    });
  }
  
  return issues;
}

/**
 * Generate comprehensive recommendations
 */
export function generateComprehensiveRecommendations(
  issues: SEOIssue[],
  metrics: SEOMetrics,
  contentAnalysis: any,
  targetKeywords: string[]
): SEORecommendation[] {
  const recommendations: SEORecommendation[] = [];
  
  // Convert high-impact issues to recommendations
  issues
    .filter(issue => issue.impact === 'high')
    .forEach(issue => {
      recommendations.push({
        title: issue.title,
        description: issue.description,
        priority: 'high',
        category: issue.category,
        action: issue.fix || 'Address this issue',
      });
    });
  
  // Content recommendations
  if (contentAnalysis.wordCount < 300) {
    recommendations.push({
      title: 'Increase Content Length',
      description: 'Add more valuable content to improve page depth and SEO',
      priority: 'medium',
      category: 'content',
      action: 'Expand content to at least 300 words',
    });
  }
  
  if (contentAnalysis.readabilityScore < 30) {
    recommendations.push({
      title: 'Improve Content Readability',
      description: 'Content is difficult to read, which may hurt user engagement',
      priority: 'medium',
      category: 'content',
      action: 'Use shorter sentences and simpler words',
    });
  }
  
  // Technical recommendations
  if (metrics.internalLinks < 3) {
    recommendations.push({
      title: 'Add More Internal Links',
      description: 'Increase internal linking to improve site navigation and SEO',
      priority: 'medium',
      category: 'technical',
      action: 'Add relevant internal links to other pages',
    });
  }
  
  if (metrics.imageOptimization.largeImages > 0) {
    recommendations.push({
      title: 'Optimize Images',
      description: 'Large images slow down page loading',
      priority: 'high',
      category: 'performance',
      action: 'Compress images and use modern formats like WebP',
    });
  }
  
  // Keyword recommendations
  targetKeywords.forEach(keyword => {
    const keywordData = contentAnalysis.keywordDensity[keyword];
    if (!keywordData || keywordData < 1) {
      recommendations.push({
        title: `Optimize for "${keyword}"`,
        description: `Target keyword "${keyword}" is underutilized`,
        priority: 'medium',
        category: 'content',
        action: `Include "${keyword}" in title, headings, and content`,
      });
    }
  });
  
  return recommendations.slice(0, 10); // Limit to top 10 recommendations
}

/**
 * Calculate comprehensive SEO score
 */
export function calculateComprehensiveScore(
  metrics: SEOMetrics,
  issues: SEOIssue[],
  contentAnalysis: any,
  performanceMetrics?: PerformanceMetrics,
  mobileOptimization?: MobileOptimization
): number {
  let score = 100;
  
  // Deduct points for issues
  issues.forEach(issue => {
    switch (issue.impact) {
      case 'high':
        score -= 15;
        break;
      case 'medium':
        score -= 8;
        break;
      case 'low':
        score -= 3;
        break;
    }
  });
  
  // Content quality adjustments
  if (contentAnalysis.wordCount < 300) score -= 10;
  if (contentAnalysis.readabilityScore < 30) score -= 10;
  if (contentAnalysis.duplicateContent) score -= 20;
  
  // Technical SEO adjustments
  if (metrics.headingStructure.h1Count !== 1) score -= 10;
  if (metrics.internalLinks < 3) score -= 5;
  if (metrics.imageOptimization.imagesWithoutAlt > 0) score -= 5;
  
  // Performance adjustments
  if (performanceMetrics) {
    if (performanceMetrics.firstContentfulPaint > PERFORMANCE_THRESHOLDS.GOOD_FCP) score -= 10;
    if (performanceMetrics.largestContentfulPaint > PERFORMANCE_THRESHOLDS.GOOD_LCP) score -= 10;
    if (performanceMetrics.cumulativeLayoutShift > PERFORMANCE_THRESHOLDS.GOOD_CLS) score -= 5;
  }
  
  // Mobile optimization adjustments
  if (mobileOptimization) {
    if (!mobileOptimization.isResponsive) score -= 20;
    if (!mobileOptimization.viewportConfigured) score -= 10;
    if (!mobileOptimization.touchTargetsOptimal) score -= 5;
  }
  
  return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * Get SEO score grade
 */
export function getSEOGrade(score: number): { grade: string; color: string; description: string } {
  if (score >= 90) {
    return {
      grade: 'A+',
      color: 'green',
      description: 'Excellent SEO optimization',
    };
  } else if (score >= 80) {
    return {
      grade: 'A',
      color: 'green',
      description: 'Very good SEO optimization',
    };
  } else if (score >= 70) {
    return {
      grade: 'B',
      color: 'yellow',
      description: 'Good SEO optimization with room for improvement',
    };
  } else if (score >= 60) {
    return {
      grade: 'C',
      color: 'orange',
      description: 'Fair SEO optimization, needs improvement',
    };
  } else if (score >= 50) {
    return {
      grade: 'D',
      color: 'red',
      description: 'Poor SEO optimization, significant issues',
    };
  } else {
    return {
      grade: 'F',
      color: 'red',
      description: 'Very poor SEO optimization, major issues',
    };
  }
}
