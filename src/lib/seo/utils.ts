import { seoConfig } from './config';

/**
 * Generate SEO-friendly URL slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Generate canonical URL
 */
export function generateCanonicalURL(path: string, locale?: string): string {
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  const localePath = locale ? `${locale}/${cleanPath}` : cleanPath;
  return `${seoConfig.baseUrl}/${localePath}`.replace(/\/+/g, '/').replace(/\/$/, '') || seoConfig.baseUrl;
}

/**
 * Extract keywords from text
 */
export function extractKeywords(text: string, maxKeywords: number = 10): string[] {
  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2)
    .filter(word => !isStopWord(word));

  const wordCount: Record<string, number> = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  return Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word);
}

/**
 * Check if word is a stop word
 */
export function isStopWord(word: string): boolean {
  const stopWords = [
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很',
    '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是',
  ];
  return stopWords.includes(word.toLowerCase());
}

/**
 * Calculate reading time
 */
export function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {
  const wordCount = text.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Generate meta description from content
 */
export function generateMetaDescription(content: string, maxLength: number = 160): string {
  // Remove HTML tags and extra whitespace
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }

  // Find the last complete sentence within the limit
  const truncated = cleanContent.substring(0, maxLength);
  const lastSentenceEnd = Math.max(
    truncated.lastIndexOf('.'),
    truncated.lastIndexOf('!'),
    truncated.lastIndexOf('?')
  );

  if (lastSentenceEnd > maxLength * 0.7) {
    return truncated.substring(0, lastSentenceEnd + 1);
  }

  // If no good sentence break, truncate at word boundary
  const lastSpace = truncated.lastIndexOf(' ');
  return truncated.substring(0, lastSpace) + '...';
}

/**
 * Validate URL structure
 */
export function validateURL(url: string): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  
  try {
    new URL(url);
  } catch {
    return { isValid: false, issues: ['Invalid URL format'] };
  }

  if (url.length > 100) {
    issues.push('URL is too long (over 100 characters)');
  }

  if (url.includes(' ')) {
    issues.push('URL contains spaces');
  }

  if (url.match(/[A-Z]/)) {
    issues.push('URL contains uppercase letters');
  }

  if (url.includes('_')) {
    issues.push('URL contains underscores (use hyphens instead)');
  }

  const specialChars = /[!@#$%^&*()+=\[\]{}|\\:";'<>?,]/;
  if (specialChars.test(url)) {
    issues.push('URL contains special characters');
  }

  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * Generate breadcrumb data
 */
export function generateBreadcrumbs(pathname: string, locale: string = 'zh'): Array<{ name: string; url: string }> {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: Array<{ name: string; url: string }> = [];

  // Add home
  breadcrumbs.push({
    name: locale === 'zh' ? '首页' : 'Home',
    url: `/${locale}`,
  });

  let currentPath = `/${locale}`;
  
  for (let i = 1; i < segments.length; i++) {
    const segment = segments[i];
    currentPath += `/${segment}`;
    
    let name = segment;
    
    // Customize names based on segment
    switch (segment) {
      case 'tools':
        name = locale === 'zh' ? '工具' : 'Tools';
        break;
      case 'blog':
        name = locale === 'zh' ? '博客' : 'Blog';
        break;
      case 'about':
        name = locale === 'zh' ? '关于' : 'About';
        break;
      case 'contact':
        name = locale === 'zh' ? '联系' : 'Contact';
        break;
      case 'help':
        name = locale === 'zh' ? '帮助' : 'Help';
        break;
      default:
        // Convert kebab-case to title case
        name = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }
    
    breadcrumbs.push({
      name,
      url: currentPath,
    });
  }

  return breadcrumbs;
}

/**
 * Optimize image alt text
 */
export function optimizeImageAlt(
  filename: string,
  context?: string,
  maxLength: number = 125
): string {
  // Extract meaningful name from filename
  const baseName = filename
    .split('.')[0]
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  let altText = baseName;
  
  if (context) {
    altText = `${context} - ${baseName}`;
  }

  // Truncate if too long
  if (altText.length > maxLength) {
    altText = altText.substring(0, maxLength - 3) + '...';
  }

  return altText;
}

/**
 * Generate robots meta content
 */
export function generateRobotsContent(options: {
  index?: boolean;
  follow?: boolean;
  noarchive?: boolean;
  nosnippet?: boolean;
  noimageindex?: boolean;
}): string {
  const directives: string[] = [];

  if (options.index === false) directives.push('noindex');
  if (options.follow === false) directives.push('nofollow');
  if (options.noarchive) directives.push('noarchive');
  if (options.nosnippet) directives.push('nosnippet');
  if (options.noimageindex) directives.push('noimageindex');

  return directives.length > 0 ? directives.join(', ') : 'index, follow';
}

/**
 * Extract text content from HTML
 */
export function extractTextFromHTML(html: string): string {
  return html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Calculate content freshness score
 */
export function calculateFreshnessScore(
  publishedDate: Date,
  lastModified?: Date
): number {
  const now = new Date();
  const daysSincePublished = (now.getTime() - publishedDate.getTime()) / (1000 * 60 * 60 * 24);
  const daysSinceModified = lastModified 
    ? (now.getTime() - lastModified.getTime()) / (1000 * 60 * 60 * 24)
    : daysSincePublished;

  // Fresh content gets higher score
  if (daysSinceModified < 7) return 100;
  if (daysSinceModified < 30) return 90;
  if (daysSinceModified < 90) return 80;
  if (daysSinceModified < 180) return 70;
  if (daysSinceModified < 365) return 60;
  return 50;
}

/**
 * Generate hreflang attributes
 */
export function generateHreflangAttributes(
  pathname: string,
  supportedLocales: string[]
): Record<string, string> {
  const hreflang: Record<string, string> = {};
  
  supportedLocales.forEach(locale => {
    const localizedPath = pathname.replace(/^\/[a-z]{2}\//, `/${locale}/`);
    hreflang[locale] = `${seoConfig.baseUrl}${localizedPath}`;
  });

  return hreflang;
}

/**
 * Validate schema markup
 */
export function validateSchemaMarkup(schema: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!schema['@context']) {
    errors.push('Missing @context property');
  }

  if (!schema['@type']) {
    errors.push('Missing @type property');
  }

  // Basic validation for common schema types
  switch (schema['@type']) {
    case 'WebSite':
      if (!schema.name) errors.push('WebSite schema missing name');
      if (!schema.url) errors.push('WebSite schema missing url');
      break;
    case 'Organization':
      if (!schema.name) errors.push('Organization schema missing name');
      break;
    case 'Article':
      if (!schema.headline) errors.push('Article schema missing headline');
      if (!schema.author) errors.push('Article schema missing author');
      if (!schema.datePublished) errors.push('Article schema missing datePublished');
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Generate social media sharing URLs
 */
export function generateSocialSharingURLs(
  url: string,
  title: string,
  description?: string
): Record<string, string> {
  const encodedURL = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = description ? encodeURIComponent(description) : '';

  return {
    twitter: `https://twitter.com/intent/tweet?url=${encodedURL}&text=${encodedTitle}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedURL}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedURL}`,
    reddit: `https://reddit.com/submit?url=${encodedURL}&title=${encodedTitle}`,
    telegram: `https://t.me/share/url?url=${encodedURL}&text=${encodedTitle}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedURL}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedURL}`,
  };
}
