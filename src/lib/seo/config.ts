import { SEOConfig } from './types';

// SEO Configuration
export const seoConfig: SEOConfig = {
  baseUrl: 'https://bili-tool.com',
  defaultLocale: 'zh',
  supportedLocales: ['zh', 'en'],
  siteName: 'Bili Tool',
  defaultTitle: '哔哩工具箱 (Bili Tool) - 高效的在线工具集合平台',
  defaultDescription: '哔哩工具箱(Bili Tool)提供全面的在线工具集合，包括图像处理、文本编辑、数据转换等多种实用功能，助您提高工作效率。',
  defaultKeywords: 'bili tool, bili-tool.com, 哔哩工具箱, 在线工具, 图像处理, 文本编辑, 工具集合, 在线应用, 提高效率',
  twitterHandle: '@bilitool',
  googleSiteVerification: 'your-google-verification-token',
  bingSiteVerification: 'your-bing-verification-token',
  yandexSiteVerification: 'your-yandex-verification-token',
};

// Localized SEO configurations
export const localizedSEOConfig = {
  zh: {
    siteName: '哔哩工具箱',
    defaultTitle: '哔哩工具箱 (Bili Tool) - 高效的在线工具集合平台',
    defaultDescription: '哔哩工具箱(Bili Tool)提供全面的在线工具集合，包括图像处理、文本编辑、数据转换等多种实用功能，助您提高工作效率。',
    defaultKeywords: 'bili tool, bili-tool.com, 哔哩工具箱, 在线工具, 图像处理, 文本编辑, 工具集合, 在线应用, 提高效率',
  },
  en: {
    siteName: 'Bili Tool',
    defaultTitle: 'Bili Tool - Efficient Online Toolbox Collection Platform',
    defaultDescription: 'Bili Tool offers a comprehensive collection of online tools, including image processing, text editing, data conversion, and more practical functions to improve your work efficiency.',
    defaultKeywords: 'bili tool, bili-tool.com, online toolbox, image processing, text editing, tool collection, online applications, productivity tools',
  },
};

// SEO best practices constants
export const SEO_LIMITS = {
  TITLE_MIN_LENGTH: 30,
  TITLE_MAX_LENGTH: 60,
  DESCRIPTION_MIN_LENGTH: 120,
  DESCRIPTION_MAX_LENGTH: 160,
  KEYWORDS_MAX_COUNT: 10,
  URL_MAX_LENGTH: 100,
  H1_MAX_COUNT: 1,
  IMAGE_ALT_MAX_LENGTH: 125,
  KEYWORD_DENSITY_MAX: 3, // percentage
  INTERNAL_LINKS_MIN: 3,
  EXTERNAL_LINKS_MAX: 10,
};

// Common schema types for the toolbox
export const SCHEMA_TYPES = {
  WEBSITE: 'WebSite',
  WEB_APPLICATION: 'WebApplication',
  TOOL: 'SoftwareApplication',
  ARTICLE: 'Article',
  BLOG_POSTING: 'BlogPosting',
  ORGANIZATION: 'Organization',
  BREADCRUMB_LIST: 'BreadcrumbList',
  FAQ_PAGE: 'FAQPage',
  HOW_TO: 'HowTo',
  PRODUCT: 'Product',
  SERVICE: 'Service',
};

// Priority levels for sitemap
export const SITEMAP_PRIORITIES = {
  HOME: 1.0,
  TOOLS_INDEX: 0.9,
  POPULAR_TOOLS: 0.8,
  TOOLS: 0.7,
  BLOG_INDEX: 0.8,
  BLOG_POSTS: 0.6,
  STATIC_PAGES: 0.5,
  LEGAL_PAGES: 0.3,
};

// Change frequencies for sitemap
export const CHANGE_FREQUENCIES = {
  ALWAYS: 'always' as const,
  HOURLY: 'hourly' as const,
  DAILY: 'daily' as const,
  WEEKLY: 'weekly' as const,
  MONTHLY: 'monthly' as const,
  YEARLY: 'yearly' as const,
  NEVER: 'never' as const,
};

// Default robots directives
export const DEFAULT_ROBOTS = {
  index: true,
  follow: true,
  noarchive: false,
  nosnippet: false,
  noimageindex: false,
};

// SEO scoring weights
export const SEO_SCORING_WEIGHTS = {
  TITLE: 20,
  DESCRIPTION: 15,
  HEADINGS: 15,
  CONTENT: 20,
  IMAGES: 10,
  LINKS: 10,
  TECHNICAL: 10,
};

// Keyword analysis settings
export const KEYWORD_ANALYSIS = {
  MIN_WORD_LENGTH: 3,
  STOP_WORDS: [
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很',
    '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是',
  ],
  TARGET_DENSITY: 1.5, // percentage
  MAX_DENSITY: 3.0, // percentage
};

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  GOOD_FCP: 1800, // ms
  GOOD_LCP: 2500, // ms
  GOOD_FID: 100, // ms
  GOOD_CLS: 0.1,
  GOOD_TTI: 3800, // ms
  GOOD_TBT: 200, // ms
};

// Image optimization settings
export const IMAGE_SEO = {
  MAX_FILE_SIZE: 500000, // 500KB
  RECOMMENDED_FORMATS: ['webp', 'avif', 'jpg', 'png'],
  ALT_TEXT_MIN_LENGTH: 5,
  ALT_TEXT_MAX_LENGTH: 125,
};
