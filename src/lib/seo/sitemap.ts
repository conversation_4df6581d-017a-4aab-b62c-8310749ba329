import { MetadataRoute } from 'next';
import { SitemapEntry } from './types';
import { seoConfig, SITEMAP_PRIORITIES, CHANGE_FREQUENCIES } from './config';
import { routing } from '@/i18n/routing';
import { tools } from '@/lib/tools/data';

/**
 * Enhanced sitemap generation with dynamic content discovery
 */
export function generateEnhancedSitemap(): MetadataRoute.Sitemap {
  const baseUrl = seoConfig.baseUrl;
  const currentDate = new Date().toISOString();
  
  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Add home pages for all locales
  routing.locales.forEach(locale => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}`,
      lastModified: currentDate,
      changeFrequency: CHANGE_FREQUENCIES.DAILY,
      priority: SITEMAP_PRIORITIES.HOME,
    });
  });

  // Add tools index pages
  routing.locales.forEach(locale => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}/tools`,
      lastModified: currentDate,
      changeFrequency: CHANGE_FREQUENCIES.DAILY,
      priority: SITEMAP_PRIORITIES.TOOLS_INDEX,
    });
  });

  // Add individual tool pages
  tools.forEach(tool => {
    routing.locales.forEach(locale => {
      const priority = tool.isFavorite 
        ? SITEMAP_PRIORITIES.POPULAR_TOOLS 
        : SITEMAP_PRIORITIES.TOOLS;
        
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/tools/${tool.id}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.WEEKLY,
        priority,
      });
    });
  });

  // Add tool category pages
  const categories = getUniqueCategories();
  categories.forEach(category => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/tools/category/${generateCategorySlug(category)}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.WEEKLY,
        priority: 0.6,
      });
    });
  });

  // Add static pages
  const staticPages = [
    'about',
    'blog',
    'help',
    'contact',
    'privacy',
    'terms',
  ];

  staticPages.forEach(page => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/${page}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.MONTHLY,
        priority: SITEMAP_PRIORITIES.STATIC_PAGES,
      });
    });
  });

  // Add blog posts (if any exist)
  const blogPosts = getBlogPosts();
  blogPosts.forEach(post => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/blog/${post.slug}`,
        lastModified: post.lastModified || currentDate,
        changeFrequency: CHANGE_FREQUENCIES.MONTHLY,
        priority: SITEMAP_PRIORITIES.BLOG_POSTS,
      });
    });
  });

  return sitemapEntries;
}

/**
 * Generate XML sitemap content
 */
export function generateXMLSitemap(): string {
  const sitemap = generateEnhancedSitemap();
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

  sitemap.forEach(entry => {
    xml += '  <url>\n';
    xml += `    <loc>${entry.url}</loc>\n`;
    xml += `    <lastmod>${entry.lastModified}</lastmod>\n`;
    xml += `    <changefreq>${entry.changeFreq}</changefreq>\n`;
    xml += `    <priority>${entry.priority}</priority>\n`;
    
    // Add hreflang for multilingual support
    if (entry.url.includes('/zh/') || entry.url.includes('/en/')) {
      const baseUrl = entry.url.replace(/\/(zh|en)\//, '/');
      routing.locales.forEach(locale => {
        const localizedUrl = baseUrl.replace(seoConfig.baseUrl, `${seoConfig.baseUrl}/${locale}`);
        xml += `    <xhtml:link rel="alternate" hreflang="${locale}" href="${localizedUrl}" />\n`;
      });
    }
    
    xml += '  </url>\n';
  });

  xml += '</urlset>';
  return xml;
}

/**
 * Generate sitemap index for large sites
 */
export function generateSitemapIndex(): string {
  const sitemaps = [
    'sitemap-pages.xml',
    'sitemap-tools.xml',
    'sitemap-blog.xml',
  ];

  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  sitemaps.forEach(sitemap => {
    xml += '  <sitemap>\n';
    xml += `    <loc>${seoConfig.baseUrl}/${sitemap}</loc>\n`;
    xml += `    <lastmod>${new Date().toISOString()}</lastmod>\n`;
    xml += '  </sitemap>\n';
  });

  xml += '</sitemapindex>';
  return xml;
}

/**
 * Generate pages sitemap
 */
export function generatePagesSitemap(): MetadataRoute.Sitemap {
  const baseUrl = seoConfig.baseUrl;
  const currentDate = new Date().toISOString();
  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Home pages
  routing.locales.forEach(locale => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}`,
      lastModified: currentDate,
      changeFrequency: CHANGE_FREQUENCIES.DAILY,
      priority: SITEMAP_PRIORITIES.HOME,
    });
  });

  // Static pages
  const staticPages = ['about', 'help', 'contact', 'privacy', 'terms'];
  staticPages.forEach(page => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/${page}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.MONTHLY,
        priority: SITEMAP_PRIORITIES.STATIC_PAGES,
      });
    });
  });

  return sitemapEntries;
}

/**
 * Generate tools sitemap
 */
export function generateToolsSitemap(): MetadataRoute.Sitemap {
  const baseUrl = seoConfig.baseUrl;
  const currentDate = new Date().toISOString();
  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Tools index
  routing.locales.forEach(locale => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}/tools`,
      lastModified: currentDate,
      changeFrequency: CHANGE_FREQUENCIES.DAILY,
      priority: SITEMAP_PRIORITIES.TOOLS_INDEX,
    });
  });

  // Individual tools
  tools.forEach(tool => {
    routing.locales.forEach(locale => {
      const priority = tool.isFavorite 
        ? SITEMAP_PRIORITIES.POPULAR_TOOLS 
        : SITEMAP_PRIORITIES.TOOLS;
        
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/tools/${tool.id}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.WEEKLY,
        priority,
      });
    });
  });

  // Tool categories
  const categories = getUniqueCategories();
  categories.forEach(category => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/tools/category/${generateCategorySlug(category)}`,
        lastModified: currentDate,
        changeFrequency: CHANGE_FREQUENCIES.WEEKLY,
        priority: 0.6,
      });
    });
  });

  return sitemapEntries;
}

/**
 * Generate blog sitemap
 */
export function generateBlogSitemap(): MetadataRoute.Sitemap {
  const baseUrl = seoConfig.baseUrl;
  const currentDate = new Date().toISOString();
  const sitemapEntries: MetadataRoute.Sitemap = [];

  // Blog index
  routing.locales.forEach(locale => {
    sitemapEntries.push({
      url: `${baseUrl}/${locale}/blog`,
      lastModified: currentDate,
      changeFrequency: CHANGE_FREQUENCIES.DAILY,
      priority: SITEMAP_PRIORITIES.BLOG_INDEX,
    });
  });

  // Blog posts
  const blogPosts = getBlogPosts();
  blogPosts.forEach(post => {
    routing.locales.forEach(locale => {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}/blog/${post.slug}`,
        lastModified: post.lastModified || currentDate,
        changeFrequency: CHANGE_FREQUENCIES.MONTHLY,
        priority: SITEMAP_PRIORITIES.BLOG_POSTS,
      });
    });
  });

  return sitemapEntries;
}

/**
 * Get unique tool categories
 */
function getUniqueCategories(): string[] {
  const categories = new Set<string>();
  tools.forEach(tool => {
    if (tool.category) {
      categories.add(tool.category);
    }
  });
  return Array.from(categories);
}

/**
 * Generate category slug from category name
 */
function generateCategorySlug(category: string): string {
  return category
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Get blog posts (placeholder - implement based on your blog system)
 */
function getBlogPosts(): Array<{ slug: string; lastModified?: string }> {
  // This would typically fetch from your CMS or file system
  // For now, return empty array
  return [];
}

/**
 * Validate sitemap entries
 */
export function validateSitemapEntries(entries: MetadataRoute.Sitemap): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  entries.forEach((entry, index) => {
    // Validate URL
    try {
      new URL(entry.url);
    } catch {
      errors.push(`Invalid URL at index ${index}: ${entry.url}`);
    }

    // Validate priority
    if (entry.priority !== undefined && (entry.priority < 0 || entry.priority > 1)) {
      errors.push(`Invalid priority at index ${index}: ${entry.priority}`);
    }

    // Validate change frequency
    const validFrequencies = Object.values(CHANGE_FREQUENCIES);
    if (entry.changeFreq && !validFrequencies.includes(entry.changeFreq as any)) {
      errors.push(`Invalid change frequency at index ${index}: ${entry.changeFreq}`);
    }

    // Validate last modified date
    if (entry.lastModified) {
      try {
        new Date(entry.lastModified);
      } catch {
        errors.push(`Invalid lastModified date at index ${index}: ${entry.lastModified}`);
      }
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Generate robots.txt content
 */
export function generateRobotsTxt(): string {
  let content = '# Bili Tool robots.txt\n';
  content += `# ${seoConfig.baseUrl}\n\n`;
  
  content += 'User-agent: *\n';
  content += 'Allow: /\n\n';
  
  // Disallow rules
  content += '# Disallow rules\n';
  content += 'Disallow: /api/\n';
  content += 'Disallow: /_next/\n';
  content += 'Disallow: /404\n';
  content += 'Disallow: /500\n';
  content += 'Disallow: /admin/\n';
  content += 'Disallow: /private/\n\n';
  
  // Sitemap location
  content += '# Sitemap\n';
  content += `Sitemap: ${seoConfig.baseUrl}/sitemap.xml\n`;
  
  // Additional sitemaps if using sitemap index
  content += `Sitemap: ${seoConfig.baseUrl}/sitemap-pages.xml\n`;
  content += `Sitemap: ${seoConfig.baseUrl}/sitemap-tools.xml\n`;
  content += `Sitemap: ${seoConfig.baseUrl}/sitemap-blog.xml\n\n`;
  
  // Crawl delay for specific bots (optional)
  content += '# Crawl delay for specific bots\n';
  content += 'User-agent: Bingbot\n';
  content += 'Crawl-delay: 1\n\n';
  
  return content;
}
