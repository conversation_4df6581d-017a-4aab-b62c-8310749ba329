import { 
  SEOAnalysis, 
  SEOIssue, 
  SEORecommendation, 
  SEOMetrics, 
  KeywordAnalysis, 
  URLAnalysis,
  ContentAnalysis,
  HeadingAnalysis,
  ImageSEOAnalysis
} from './types';
import { SEO_LIMITS, KEYWORD_ANALYSIS, SEO_SCORING_WEIGHTS } from './config';

/**
 * Analyze page content for SEO optimization
 */
export function analyzePage(
  title: string,
  description: string,
  content: string,
  url: string,
  headings: { level: number; text: string }[],
  images: { src: string; alt?: string; size?: number }[],
  links: { href: string; text: string; internal: boolean }[]
): SEOAnalysis {
  const metrics = calculateSEOMetrics(title, description, content, headings, images, links);
  const issues = findSEOIssues(title, description, content, url, headings, images, links);
  const recommendations = generateRecommendations(issues, metrics);
  const score = calculateSEOScore(metrics, issues);

  return {
    score,
    issues,
    recommendations,
    metrics,
  };
}

/**
 * Calculate SEO metrics
 */
export function calculateSEOMetrics(
  title: string,
  description: string,
  content: string,
  headings: { level: number; text: string }[],
  images: { src: string; alt?: string; size?: number }[],
  links: { href: string; text: string; internal: boolean }[]
): SEOMetrics {
  const keywordDensity = analyzeKeywordDensity(content);
  const headingStructure = analyzeHeadingStructure(headings);
  const imageOptimization = analyzeImageSEO(images);
  
  return {
    titleLength: title.length,
    descriptionLength: description.length,
    keywordDensity,
    headingStructure,
    imageOptimization,
    internalLinks: links.filter(link => link.internal).length,
    externalLinks: links.filter(link => !link.internal).length,
    pageSize: content.length,
  };
}

/**
 * Analyze keyword density
 */
export function analyzeKeywordDensity(content: string): Record<string, number> {
  const words = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => 
      word.length >= KEYWORD_ANALYSIS.MIN_WORD_LENGTH && 
      !KEYWORD_ANALYSIS.STOP_WORDS.includes(word)
    );

  const totalWords = words.length;
  const wordCount: Record<string, number> = {};

  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  const density: Record<string, number> = {};
  Object.entries(wordCount).forEach(([word, count]) => {
    density[word] = (count / totalWords) * 100;
  });

  // Return top 20 keywords by density
  return Object.fromEntries(
    Object.entries(density)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
  );
}

/**
 * Analyze heading structure
 */
export function analyzeHeadingStructure(
  headings: { level: number; text: string }[]
): HeadingAnalysis {
  const counts = {
    h1Count: headings.filter(h => h.level === 1).length,
    h2Count: headings.filter(h => h.level === 2).length,
    h3Count: headings.filter(h => h.level === 3).length,
    h4Count: headings.filter(h => h.level === 4).length,
    h5Count: headings.filter(h => h.level === 5).length,
    h6Count: headings.filter(h => h.level === 6).length,
  };

  const structure = headings.map((heading, index) => ({
    level: heading.level,
    text: heading.text,
    position: index + 1,
  }));

  return {
    ...counts,
    structure,
  };
}

/**
 * Analyze image SEO
 */
export function analyzeImageSEO(
  images: { src: string; alt?: string; size?: number }[]
): ImageSEOAnalysis {
  const totalImages = images.length;
  const imagesWithAlt = images.filter(img => img.alt && img.alt.trim().length > 0).length;
  const imagesWithoutAlt = totalImages - imagesWithAlt;
  const largeImages = images.filter(img => img.size && img.size > 500000).length; // > 500KB
  const optimizedImages = images.filter(img => 
    img.src.includes('.webp') || 
    img.src.includes('.avif') ||
    (img.size && img.size <= 500000)
  ).length;

  return {
    totalImages,
    imagesWithAlt,
    imagesWithoutAlt,
    largeImages,
    optimizedImages,
  };
}

/**
 * Find SEO issues
 */
export function findSEOIssues(
  title: string,
  description: string,
  content: string,
  url: string,
  headings: { level: number; text: string }[],
  images: { src: string; alt?: string; size?: number }[],
  links: { href: string; text: string; internal: boolean }[]
): SEOIssue[] {
  const issues: SEOIssue[] = [];

  // Title issues
  if (!title) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing Title',
      description: 'Page title is missing',
      impact: 'high',
      fix: 'Add a descriptive title tag',
    });
  } else {
    if (title.length < SEO_LIMITS.TITLE_MIN_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Title Too Short',
        description: `Title is ${title.length} characters, should be at least ${SEO_LIMITS.TITLE_MIN_LENGTH}`,
        impact: 'medium',
        fix: 'Expand the title with more descriptive keywords',
      });
    }
    if (title.length > SEO_LIMITS.TITLE_MAX_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Title Too Long',
        description: `Title is ${title.length} characters, should be at most ${SEO_LIMITS.TITLE_MAX_LENGTH}`,
        impact: 'medium',
        fix: 'Shorten the title while keeping key information',
      });
    }
  }

  // Description issues
  if (!description) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing Meta Description',
      description: 'Meta description is missing',
      impact: 'high',
      fix: 'Add a compelling meta description',
    });
  } else {
    if (description.length < SEO_LIMITS.DESCRIPTION_MIN_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Description Too Short',
        description: `Description is ${description.length} characters, should be at least ${SEO_LIMITS.DESCRIPTION_MIN_LENGTH}`,
        impact: 'medium',
        fix: 'Expand the description with more details',
      });
    }
    if (description.length > SEO_LIMITS.DESCRIPTION_MAX_LENGTH) {
      issues.push({
        type: 'warning',
        category: 'content',
        title: 'Description Too Long',
        description: `Description is ${description.length} characters, should be at most ${SEO_LIMITS.DESCRIPTION_MAX_LENGTH}`,
        impact: 'medium',
        fix: 'Shorten the description while keeping key information',
      });
    }
  }

  // Heading issues
  const h1Count = headings.filter(h => h.level === 1).length;
  if (h1Count === 0) {
    issues.push({
      type: 'error',
      category: 'content',
      title: 'Missing H1 Tag',
      description: 'Page is missing an H1 heading',
      impact: 'high',
      fix: 'Add a descriptive H1 heading',
    });
  } else if (h1Count > 1) {
    issues.push({
      type: 'warning',
      category: 'content',
      title: 'Multiple H1 Tags',
      description: `Page has ${h1Count} H1 tags, should have only one`,
      impact: 'medium',
      fix: 'Use only one H1 tag per page',
    });
  }

  // Image issues
  const imagesWithoutAlt = images.filter(img => !img.alt || img.alt.trim().length === 0);
  if (imagesWithoutAlt.length > 0) {
    issues.push({
      type: 'warning',
      category: 'accessibility',
      title: 'Images Missing Alt Text',
      description: `${imagesWithoutAlt.length} images are missing alt text`,
      impact: 'medium',
      fix: 'Add descriptive alt text to all images',
    });
  }

  // URL issues
  if (url.length > SEO_LIMITS.URL_MAX_LENGTH) {
    issues.push({
      type: 'warning',
      category: 'technical',
      title: 'URL Too Long',
      description: `URL is ${url.length} characters, should be under ${SEO_LIMITS.URL_MAX_LENGTH}`,
      impact: 'low',
      fix: 'Shorten the URL structure',
    });
  }

  // Content issues
  const wordCount = content.split(/\s+/).length;
  if (wordCount < 300) {
    issues.push({
      type: 'warning',
      category: 'content',
      title: 'Thin Content',
      description: `Page has only ${wordCount} words, consider adding more content`,
      impact: 'medium',
      fix: 'Add more valuable content to improve page depth',
    });
  }

  return issues;
}

/**
 * Generate SEO recommendations
 */
export function generateRecommendations(
  issues: SEOIssue[],
  metrics: SEOMetrics
): SEORecommendation[] {
  const recommendations: SEORecommendation[] = [];

  // Convert high-impact issues to recommendations
  issues
    .filter(issue => issue.impact === 'high')
    .forEach(issue => {
      recommendations.push({
        title: issue.title,
        description: issue.description,
        priority: 'high',
        category: issue.category,
        action: issue.fix || 'Address this issue',
      });
    });

  // Add general recommendations based on metrics
  if (metrics.internalLinks < SEO_LIMITS.INTERNAL_LINKS_MIN) {
    recommendations.push({
      title: 'Add More Internal Links',
      description: 'Increase internal linking to improve site navigation and SEO',
      priority: 'medium',
      category: 'technical',
      action: 'Add relevant internal links to other pages',
    });
  }

  if (metrics.imageOptimization.largeImages > 0) {
    recommendations.push({
      title: 'Optimize Images',
      description: 'Compress large images to improve page load speed',
      priority: 'medium',
      category: 'performance',
      action: 'Compress images and use modern formats like WebP',
    });
  }

  return recommendations;
}

/**
 * Calculate overall SEO score
 */
export function calculateSEOScore(metrics: SEOMetrics, issues: SEOIssue[]): number {
  let score = 100;

  // Deduct points for issues
  issues.forEach(issue => {
    switch (issue.impact) {
      case 'high':
        score -= 15;
        break;
      case 'medium':
        score -= 8;
        break;
      case 'low':
        score -= 3;
        break;
    }
  });

  // Adjust based on metrics
  if (metrics.titleLength < SEO_LIMITS.TITLE_MIN_LENGTH || metrics.titleLength > SEO_LIMITS.TITLE_MAX_LENGTH) {
    score -= 10;
  }

  if (metrics.descriptionLength < SEO_LIMITS.DESCRIPTION_MIN_LENGTH || metrics.descriptionLength > SEO_LIMITS.DESCRIPTION_MAX_LENGTH) {
    score -= 10;
  }

  if (metrics.headingStructure.h1Count !== 1) {
    score -= 10;
  }

  if (metrics.imageOptimization.imagesWithoutAlt > 0) {
    score -= 5;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * Analyze specific keyword performance
 */
export function analyzeKeyword(
  keyword: string,
  title: string,
  description: string,
  content: string,
  headings: { level: number; text: string }[]
): KeywordAnalysis {
  const lowerKeyword = keyword.toLowerCase();
  const lowerContent = content.toLowerCase();
  const lowerTitle = title.toLowerCase();
  const lowerDescription = description.toLowerCase();

  // Count occurrences
  const contentMatches = (lowerContent.match(new RegExp(lowerKeyword, 'g')) || []).length;
  const totalWords = content.split(/\s+/).length;
  const density = (contentMatches / totalWords) * 100;

  // Calculate prominence
  let prominence = 0;
  if (lowerTitle.includes(lowerKeyword)) prominence += 30;
  if (lowerDescription.includes(lowerKeyword)) prominence += 20;
  
  headings.forEach(heading => {
    if (heading.text.toLowerCase().includes(lowerKeyword)) {
      prominence += heading.level === 1 ? 25 : heading.level === 2 ? 15 : 10;
    }
  });

  // Generate suggestions
  const suggestions: string[] = [];
  if (!lowerTitle.includes(lowerKeyword)) {
    suggestions.push('Include keyword in title');
  }
  if (!lowerDescription.includes(lowerKeyword)) {
    suggestions.push('Include keyword in meta description');
  }
  if (density < KEYWORD_ANALYSIS.TARGET_DENSITY) {
    suggestions.push('Increase keyword density in content');
  }
  if (density > KEYWORD_ANALYSIS.MAX_DENSITY) {
    suggestions.push('Reduce keyword density to avoid over-optimization');
  }

  return {
    keyword,
    density,
    count: contentMatches,
    prominence,
    suggestions,
  };
}

/**
 * Analyze URL structure
 */
export function analyzeURL(url: string, targetKeywords: string[]): URLAnalysis {
  const length = url.length;
  const lowerURL = url.toLowerCase();
  
  // Check if URL contains keywords
  const hasKeywords = targetKeywords.some(keyword => 
    lowerURL.includes(keyword.toLowerCase())
  );

  // Assess structure quality
  let structure: 'good' | 'fair' | 'poor' = 'good';
  if (url.includes('?') || url.includes('#') || url.includes('&')) {
    structure = 'fair';
  }
  if (url.includes('%') || url.match(/\d{4,}/)) {
    structure = 'poor';
  }

  // Calculate readability (simple heuristic)
  const segments = url.split('/').filter(Boolean);
  const readability = segments.every(segment => 
    segment.length <= 20 && !segment.match(/[^a-zA-Z0-9-_]/)
  ) ? 100 : 50;

  const suggestions: string[] = [];
  if (length > SEO_LIMITS.URL_MAX_LENGTH) {
    suggestions.push('Shorten URL length');
  }
  if (!hasKeywords) {
    suggestions.push('Include target keywords in URL');
  }
  if (structure === 'poor') {
    suggestions.push('Improve URL structure and remove unnecessary parameters');
  }

  return {
    length,
    structure,
    hasKeywords,
    readability,
    suggestions,
  };
}
