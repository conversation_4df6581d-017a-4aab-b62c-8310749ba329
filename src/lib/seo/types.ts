// SEO Types and Interfaces

export interface SEOMetadata {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  robots?: {
    index?: boolean;
    follow?: boolean;
    noarchive?: boolean;
    nosnippet?: boolean;
    noimageindex?: boolean;
  };
  openGraph?: OpenGraphData;
  twitter?: TwitterCardData;
  alternates?: {
    canonical?: string;
    languages?: Record<string, string>;
  };
  verification?: {
    google?: string;
    bing?: string;
    yandex?: string;
  };
}

export interface OpenGraphData {
  title: string;
  description: string;
  url?: string;
  siteName?: string;
  images?: OpenGraphImage[];
  locale?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
  tags?: string[];
}

export interface OpenGraphImage {
  url: string;
  width?: number;
  height?: number;
  alt?: string;
  type?: string;
}

export interface TwitterCardData {
  card: 'summary' | 'summary_large_image' | 'app' | 'player';
  title: string;
  description: string;
  images?: string[];
  creator?: string;
  site?: string;
}

export interface SchemaMarkup {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

export interface BreadcrumbItem {
  name: string;
  url: string;
  position: number;
}

export interface SEOAnalysis {
  score: number;
  issues: SEOIssue[];
  recommendations: SEORecommendation[];
  metrics: SEOMetrics;
}

export interface SEOIssue {
  type: 'error' | 'warning' | 'info';
  category: 'technical' | 'content' | 'performance' | 'accessibility';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  fix?: string;
}

export interface SEORecommendation {
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'technical' | 'content' | 'performance' | 'accessibility';
  action: string;
}

export interface SEOMetrics {
  titleLength: number;
  descriptionLength: number;
  keywordDensity: Record<string, number>;
  headingStructure: HeadingAnalysis;
  imageOptimization: ImageSEOAnalysis;
  internalLinks: number;
  externalLinks: number;
  pageSize: number;
  loadTime?: number;
}

export interface HeadingAnalysis {
  h1Count: number;
  h2Count: number;
  h3Count: number;
  h4Count: number;
  h5Count: number;
  h6Count: number;
  structure: HeadingStructureItem[];
}

export interface HeadingStructureItem {
  level: number;
  text: string;
  position: number;
}

export interface ImageSEOAnalysis {
  totalImages: number;
  imagesWithAlt: number;
  imagesWithoutAlt: number;
  largeImages: number;
  optimizedImages: number;
}

export interface KeywordAnalysis {
  keyword: string;
  density: number;
  count: number;
  prominence: number;
  suggestions: string[];
}

export interface URLAnalysis {
  length: number;
  structure: 'good' | 'fair' | 'poor';
  hasKeywords: boolean;
  readability: number;
  suggestions: string[];
}

export interface SitemapEntry {
  url: string;
  lastModified: string;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  alternates?: Record<string, string>;
}

export interface RobotsDirective {
  userAgent: string;
  allow?: string[];
  disallow?: string[];
  crawlDelay?: number;
  sitemap?: string[];
}

export interface SEOConfig {
  baseUrl: string;
  defaultLocale: string;
  supportedLocales: string[];
  siteName: string;
  defaultTitle: string;
  defaultDescription: string;
  defaultKeywords: string;
  twitterHandle?: string;
  facebookAppId?: string;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
  yandexSiteVerification?: string;
}

export interface ContentAnalysis {
  wordCount: number;
  readabilityScore: number;
  keywordDensity: Record<string, number>;
  topKeywords: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  duplicateContent: boolean;
}

export interface PerformanceMetrics {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  timeToInteractive: number;
  totalBlockingTime: number;
}

export interface MobileOptimization {
  isResponsive: boolean;
  viewportConfigured: boolean;
  touchTargetsOptimal: boolean;
  fontSizeReadable: boolean;
  contentFitsViewport: boolean;
}

export interface LocalSEO {
  businessName?: string;
  address?: string;
  phone?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  businessHours?: Record<string, string>;
  businessType?: string;
}
