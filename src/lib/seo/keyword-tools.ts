import { KeywordAnalysis, ContentAnalysis } from './types';
import { KEYWORD_ANALYSIS } from './config';
import { isStopWord } from './utils';

/**
 * Advanced keyword research and optimization tools
 */

/**
 * Analyze keyword density and distribution
 */
export function analyzeKeywordDensity(
  content: string,
  targetKeywords: string[] = []
): Record<string, KeywordAnalysis> {
  const cleanContent = content.toLowerCase().replace(/[^\w\s]/g, ' ');
  const words = cleanContent.split(/\s+/).filter(word => 
    word.length >= KEYWORD_ANALYSIS.MIN_WORD_LENGTH && 
    !isStopWord(word)
  );
  
  const totalWords = words.length;
  const wordCount: Record<string, number> = {};
  
  // Count all words
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  const analyses: Record<string, KeywordAnalysis> = {};
  
  // Analyze target keywords
  targetKeywords.forEach(keyword => {
    const lowerKeyword = keyword.toLowerCase();
    const count = wordCount[lowerKeyword] || 0;
    const density = (count / totalWords) * 100;
    
    analyses[keyword] = {
      keyword,
      density,
      count,
      prominence: calculateKeywordProminence(keyword, content),
      suggestions: generateKeywordSuggestions(keyword, density, count),
    };
  });
  
  // Add top keywords from content
  const topKeywords = Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .filter(([word]) => !targetKeywords.some(tk => tk.toLowerCase() === word));
    
  topKeywords.forEach(([word, count]) => {
    const density = (count / totalWords) * 100;
    analyses[word] = {
      keyword: word,
      density,
      count,
      prominence: calculateKeywordProminence(word, content),
      suggestions: generateKeywordSuggestions(word, density, count),
    };
  });
  
  return analyses;
}

/**
 * Calculate keyword prominence in content
 */
export function calculateKeywordProminence(keyword: string, content: string): number {
  const lowerKeyword = keyword.toLowerCase();
  const lowerContent = content.toLowerCase();
  
  let prominence = 0;
  
  // Check title (first 100 characters)
  const title = lowerContent.substring(0, 100);
  if (title.includes(lowerKeyword)) {
    prominence += 30;
  }
  
  // Check first paragraph (first 300 characters)
  const firstParagraph = lowerContent.substring(0, 300);
  if (firstParagraph.includes(lowerKeyword)) {
    prominence += 20;
  }
  
  // Check headings (look for common heading patterns)
  const headingPatterns = [
    /^#{1,6}\s.*$/gm, // Markdown headings
    /<h[1-6][^>]*>.*?<\/h[1-6]>/gi, // HTML headings
  ];
  
  headingPatterns.forEach(pattern => {
    const headings = content.match(pattern) || [];
    headings.forEach(heading => {
      if (heading.toLowerCase().includes(lowerKeyword)) {
        prominence += 15;
      }
    });
  });
  
  // Check last paragraph
  const lastParagraph = lowerContent.substring(Math.max(0, lowerContent.length - 300));
  if (lastParagraph.includes(lowerKeyword)) {
    prominence += 10;
  }
  
  return Math.min(100, prominence);
}

/**
 * Generate keyword optimization suggestions
 */
export function generateKeywordSuggestions(
  keyword: string,
  density: number,
  count: number
): string[] {
  const suggestions: string[] = [];
  
  if (density < KEYWORD_ANALYSIS.TARGET_DENSITY) {
    suggestions.push(`Increase keyword density (current: ${density.toFixed(2)}%, target: ${KEYWORD_ANALYSIS.TARGET_DENSITY}%)`);
    suggestions.push('Add keyword to headings and subheadings');
    suggestions.push('Include keyword in meta description');
    suggestions.push('Use keyword in image alt text');
  }
  
  if (density > KEYWORD_ANALYSIS.MAX_DENSITY) {
    suggestions.push(`Reduce keyword density to avoid over-optimization (current: ${density.toFixed(2)}%, max: ${KEYWORD_ANALYSIS.MAX_DENSITY}%)`);
    suggestions.push('Use synonyms and related terms');
    suggestions.push('Focus on natural language flow');
  }
  
  if (count === 0) {
    suggestions.push('Add keyword to content');
    suggestions.push('Include keyword in title');
    suggestions.push('Use keyword in first paragraph');
  }
  
  if (count === 1) {
    suggestions.push('Add keyword variations');
    suggestions.push('Include keyword in conclusion');
  }
  
  return suggestions;
}

/**
 * Suggest related keywords
 */
export function suggestRelatedKeywords(
  primaryKeyword: string,
  content: string
): string[] {
  const suggestions: string[] = [];
  const lowerKeyword = primaryKeyword.toLowerCase();
  
  // Common keyword variations
  const variations = [
    `${lowerKeyword} tool`,
    `${lowerKeyword} online`,
    `free ${lowerKeyword}`,
    `${lowerKeyword} generator`,
    `${lowerKeyword} converter`,
    `best ${lowerKeyword}`,
    `how to ${lowerKeyword}`,
    `${lowerKeyword} guide`,
    `${lowerKeyword} tutorial`,
    `${lowerKeyword} tips`,
  ];
  
  // Filter variations that make sense
  variations.forEach(variation => {
    if (!content.toLowerCase().includes(variation)) {
      suggestions.push(variation);
    }
  });
  
  // Add semantic variations based on keyword type
  if (lowerKeyword.includes('convert') || lowerKeyword.includes('transform')) {
    suggestions.push('format conversion', 'file transformation', 'data conversion');
  }
  
  if (lowerKeyword.includes('generate') || lowerKeyword.includes('create')) {
    suggestions.push('generator tool', 'creation utility', 'maker application');
  }
  
  if (lowerKeyword.includes('analyze') || lowerKeyword.includes('check')) {
    suggestions.push('analysis tool', 'checker utility', 'validation service');
  }
  
  return suggestions.slice(0, 10);
}

/**
 * Analyze content readability and SEO
 */
export function analyzeContentSEO(
  title: string,
  description: string,
  content: string,
  targetKeywords: string[] = []
): ContentAnalysis {
  const wordCount = content.split(/\s+/).length;
  const readabilityScore = calculateReadabilityScore(content);
  const keywordDensity = analyzeKeywordDensity(content, targetKeywords);
  const topKeywords = extractTopKeywords(content, 10);
  const sentiment = analyzeSentiment(content);
  const duplicateContent = checkDuplicateContent(content);
  
  return {
    wordCount,
    readabilityScore,
    keywordDensity: Object.fromEntries(
      Object.entries(keywordDensity).map(([k, v]) => [k, v.density])
    ),
    topKeywords,
    sentiment,
    duplicateContent,
  };
}

/**
 * Calculate readability score (simplified Flesch Reading Ease)
 */
export function calculateReadabilityScore(content: string): number {
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = content.split(/\s+/).filter(w => w.length > 0);
  const syllables = words.reduce((total, word) => total + countSyllables(word), 0);
  
  if (sentences.length === 0 || words.length === 0) return 0;
  
  const avgSentenceLength = words.length / sentences.length;
  const avgSyllablesPerWord = syllables / words.length;
  
  // Simplified Flesch Reading Ease formula
  const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  
  return Math.max(0, Math.min(100, score));
}

/**
 * Count syllables in a word (simplified)
 */
function countSyllables(word: string): number {
  const vowels = 'aeiouy';
  let count = 0;
  let previousWasVowel = false;
  
  for (let i = 0; i < word.length; i++) {
    const isVowel = vowels.includes(word[i].toLowerCase());
    if (isVowel && !previousWasVowel) {
      count++;
    }
    previousWasVowel = isVowel;
  }
  
  // Handle silent 'e'
  if (word.endsWith('e') && count > 1) {
    count--;
  }
  
  return Math.max(1, count);
}

/**
 * Extract top keywords from content
 */
export function extractTopKeywords(content: string, limit: number = 10): string[] {
  const words = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => 
      word.length >= KEYWORD_ANALYSIS.MIN_WORD_LENGTH && 
      !isStopWord(word)
    );
  
  const wordCount: Record<string, number> = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  return Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([word]) => word);
}

/**
 * Analyze content sentiment (simplified)
 */
export function analyzeSentiment(content: string): 'positive' | 'neutral' | 'negative' {
  const positiveWords = [
    'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'best', 'perfect',
    'love', 'like', 'enjoy', 'happy', 'pleased', 'satisfied', 'recommend', 'useful',
    '好', '棒', '优秀', '很好', '喜欢', '满意', '推荐', '有用', '完美', '出色'
  ];
  
  const negativeWords = [
    'bad', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'dislike', 'disappointed',
    'frustrated', 'annoying', 'useless', 'broken', 'problem', 'issue', 'error',
    '坏', '糟糕', '讨厌', '失望', '问题', '错误', '无用', '烦人', '不好'
  ];
  
  const words = content.toLowerCase().split(/\s+/);
  let positiveCount = 0;
  let negativeCount = 0;
  
  words.forEach(word => {
    if (positiveWords.includes(word)) positiveCount++;
    if (negativeWords.includes(word)) negativeCount++;
  });
  
  const sentimentScore = positiveCount - negativeCount;
  
  if (sentimentScore > 2) return 'positive';
  if (sentimentScore < -2) return 'negative';
  return 'neutral';
}

/**
 * Check for duplicate content (simplified)
 */
export function checkDuplicateContent(content: string): boolean {
  // This is a simplified check - in a real implementation,
  // you would compare against a database of existing content
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
  const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
  
  // If more than 30% of sentences are duplicates, flag as duplicate content
  const duplicateRatio = (sentences.length - uniqueSentences.size) / sentences.length;
  return duplicateRatio > 0.3;
}

/**
 * Generate content optimization recommendations
 */
export function generateContentRecommendations(
  analysis: ContentAnalysis,
  targetKeywords: string[]
): string[] {
  const recommendations: string[] = [];
  
  // Word count recommendations
  if (analysis.wordCount < 300) {
    recommendations.push('Increase content length to at least 300 words for better SEO');
  } else if (analysis.wordCount > 2000) {
    recommendations.push('Consider breaking long content into multiple pages or sections');
  }
  
  // Readability recommendations
  if (analysis.readabilityScore < 30) {
    recommendations.push('Improve readability by using shorter sentences and simpler words');
  } else if (analysis.readabilityScore > 90) {
    recommendations.push('Content might be too simple - consider adding more detailed information');
  }
  
  // Keyword recommendations
  targetKeywords.forEach(keyword => {
    const density = analysis.keywordDensity[keyword] || 0;
    if (density < KEYWORD_ANALYSIS.TARGET_DENSITY) {
      recommendations.push(`Increase density for keyword "${keyword}"`);
    } else if (density > KEYWORD_ANALYSIS.MAX_DENSITY) {
      recommendations.push(`Reduce density for keyword "${keyword}" to avoid over-optimization`);
    }
  });
  
  // Sentiment recommendations
  if (analysis.sentiment === 'negative') {
    recommendations.push('Consider using more positive language to improve user engagement');
  }
  
  // Duplicate content warning
  if (analysis.duplicateContent) {
    recommendations.push('Review content for potential duplication and ensure uniqueness');
  }
  
  return recommendations;
}
