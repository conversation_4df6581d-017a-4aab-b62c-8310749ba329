import { MobileOptimization, PerformanceMetrics } from './types';
import { seoConfig } from './config';

/**
 * Technical SEO utilities and optimizations
 */

/**
 * Generate canonical URL with proper formatting
 */
export function generateCanonicalURL(
  pathname: string, 
  locale?: string,
  removeParams: boolean = true
): string {
  let url = pathname;
  
  // Remove query parameters if requested
  if (removeParams && url.includes('?')) {
    url = url.split('?')[0];
  }
  
  // Remove hash fragments
  if (url.includes('#')) {
    url = url.split('#')[0];
  }
  
  // Ensure proper locale prefix
  if (locale) {
    // Remove existing locale prefix
    url = url.replace(/^\/[a-z]{2}\//, '/');
    // Add correct locale prefix
    url = `/${locale}${url}`;
  }
  
  // Ensure single leading slash
  url = url.replace(/^\/+/, '/');
  
  // Remove trailing slash except for root
  if (url.length > 1 && url.endsWith('/')) {
    url = url.slice(0, -1);
  }
  
  return `${seoConfig.baseUrl}${url}`;
}

/**
 * Generate hreflang attributes for multilingual pages
 */
export function generateHreflangTags(
  pathname: string,
  supportedLocales: string[] = ['zh', 'en']
): Array<{ hreflang: string; href: string }> {
  const hreflangTags: Array<{ hreflang: string; href: string }> = [];
  
  // Remove existing locale from pathname
  const basePath = pathname.replace(/^\/[a-z]{2}\//, '/');
  
  supportedLocales.forEach(locale => {
    const localizedPath = `/${locale}${basePath === '/' ? '' : basePath}`;
    hreflangTags.push({
      hreflang: locale,
      href: `${seoConfig.baseUrl}${localizedPath}`,
    });
  });
  
  // Add x-default for international targeting
  hreflangTags.push({
    hreflang: 'x-default',
    href: `${seoConfig.baseUrl}${basePath}`,
  });
  
  return hreflangTags;
}

/**
 * Check mobile responsiveness
 */
export function checkMobileResponsiveness(
  viewport: string,
  cssRules: string[],
  touchTargets: Array<{ width: number; height: number }>
): MobileOptimization {
  // Check viewport meta tag
  const viewportConfigured = viewport.includes('width=device-width') && 
                            viewport.includes('initial-scale=1');
  
  // Check for responsive CSS
  const hasMediaQueries = cssRules.some(rule => 
    rule.includes('@media') && 
    (rule.includes('max-width') || rule.includes('min-width'))
  );
  
  // Check touch target sizes (minimum 44px recommended)
  const touchTargetsOptimal = touchTargets.every(target => 
    target.width >= 44 && target.height >= 44
  );
  
  // Check for responsive units
  const hasResponsiveUnits = cssRules.some(rule => 
    rule.includes('%') || 
    rule.includes('vw') || 
    rule.includes('vh') || 
    rule.includes('rem') || 
    rule.includes('em')
  );
  
  // Check font size (minimum 16px for readability)
  const fontSizeReadable = cssRules.some(rule => {
    const fontSizeMatch = rule.match(/font-size:\s*(\d+)px/);
    return fontSizeMatch ? parseInt(fontSizeMatch[1]) >= 16 : true;
  });
  
  return {
    isResponsive: hasMediaQueries && hasResponsiveUnits,
    viewportConfigured,
    touchTargetsOptimal,
    fontSizeReadable,
    contentFitsViewport: hasResponsiveUnits,
  };
}

/**
 * Analyze page speed and performance
 */
export function analyzePageSpeed(
  loadTime: number,
  resourceSizes: Record<string, number>,
  renderMetrics: Partial<PerformanceMetrics>
): {
  score: number;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  let score = 100;
  
  // Load time analysis
  if (loadTime > 3000) {
    issues.push(`Page load time is ${loadTime}ms (should be under 3000ms)`);
    recommendations.push('Optimize server response time and reduce resource sizes');
    score -= 20;
  } else if (loadTime > 2000) {
    issues.push(`Page load time is ${loadTime}ms (good but could be better)`);
    recommendations.push('Consider further optimization for better user experience');
    score -= 10;
  }
  
  // Resource size analysis
  const totalSize = Object.values(resourceSizes).reduce((sum, size) => sum + size, 0);
  if (totalSize > 2000000) { // 2MB
    issues.push(`Total page size is ${(totalSize / 1024 / 1024).toFixed(2)}MB (should be under 2MB)`);
    recommendations.push('Compress images and minify CSS/JavaScript');
    score -= 15;
  }
  
  // Image optimization
  if (resourceSizes.images && resourceSizes.images > 1000000) { // 1MB
    issues.push('Images are too large');
    recommendations.push('Optimize and compress images, use modern formats like WebP');
    score -= 10;
  }
  
  // JavaScript size
  if (resourceSizes.javascript && resourceSizes.javascript > 500000) { // 500KB
    issues.push('JavaScript bundle is too large');
    recommendations.push('Split JavaScript bundles and implement code splitting');
    score -= 10;
  }
  
  // CSS size
  if (resourceSizes.css && resourceSizes.css > 200000) { // 200KB
    issues.push('CSS files are too large');
    recommendations.push('Minify CSS and remove unused styles');
    score -= 5;
  }
  
  // Core Web Vitals analysis
  if (renderMetrics.firstContentfulPaint && renderMetrics.firstContentfulPaint > 1800) {
    issues.push(`First Contentful Paint is ${renderMetrics.firstContentfulPaint}ms (should be under 1800ms)`);
    recommendations.push('Optimize critical rendering path');
    score -= 15;
  }
  
  if (renderMetrics.largestContentfulPaint && renderMetrics.largestContentfulPaint > 2500) {
    issues.push(`Largest Contentful Paint is ${renderMetrics.largestContentfulPaint}ms (should be under 2500ms)`);
    recommendations.push('Optimize largest content element loading');
    score -= 15;
  }
  
  if (renderMetrics.cumulativeLayoutShift && renderMetrics.cumulativeLayoutShift > 0.1) {
    issues.push(`Cumulative Layout Shift is ${renderMetrics.cumulativeLayoutShift} (should be under 0.1)`);
    recommendations.push('Set explicit dimensions for images and avoid inserting content above existing content');
    score -= 10;
  }
  
  return {
    score: Math.max(0, score),
    issues,
    recommendations,
  };
}

/**
 * Generate structured data for breadcrumbs
 */
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url: string }>
): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

/**
 * Optimize images for SEO
 */
export function optimizeImageSEO(
  images: Array<{
    src: string;
    alt?: string;
    width?: number;
    height?: number;
    size?: number;
  }>
): {
  optimized: Array<{
    src: string;
    alt: string;
    width?: number;
    height?: number;
    loading?: 'lazy' | 'eager';
    decoding?: 'async' | 'sync' | 'auto';
  }>;
  issues: string[];
  recommendations: string[];
} {
  const optimized = [];
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  images.forEach((img, index) => {
    const optimizedImg: any = { ...img };
    
    // Alt text optimization
    if (!img.alt || img.alt.trim().length === 0) {
      issues.push(`Image ${index + 1} is missing alt text`);
      recommendations.push(`Add descriptive alt text for image: ${img.src}`);
      optimizedImg.alt = `Image ${index + 1}`; // Fallback
    } else if (img.alt.length > 125) {
      issues.push(`Image ${index + 1} alt text is too long (${img.alt.length} characters)`);
      recommendations.push('Keep alt text under 125 characters');
      optimizedImg.alt = img.alt.substring(0, 122) + '...';
    } else {
      optimizedImg.alt = img.alt;
    }
    
    // Lazy loading for non-critical images
    if (index > 2) { // First 3 images load eagerly
      optimizedImg.loading = 'lazy';
      optimizedImg.decoding = 'async';
    } else {
      optimizedImg.loading = 'eager';
    }
    
    // Size optimization
    if (img.size && img.size > 500000) { // 500KB
      issues.push(`Image ${index + 1} is too large (${(img.size / 1024).toFixed(0)}KB)`);
      recommendations.push('Compress image and consider using WebP format');
    }
    
    // Dimension optimization
    if (!img.width || !img.height) {
      issues.push(`Image ${index + 1} is missing width/height attributes`);
      recommendations.push('Add explicit width and height attributes to prevent layout shift');
    }
    
    optimized.push(optimizedImg);
  });
  
  return {
    optimized,
    issues,
    recommendations,
  };
}

/**
 * Generate robots meta tag content
 */
export function generateRobotsMeta(options: {
  index?: boolean;
  follow?: boolean;
  noarchive?: boolean;
  nosnippet?: boolean;
  noimageindex?: boolean;
  maxSnippet?: number;
  maxImagePreview?: 'none' | 'standard' | 'large';
  maxVideoPreview?: number;
}): string {
  const directives: string[] = [];
  
  // Basic directives
  if (options.index === false) directives.push('noindex');
  if (options.follow === false) directives.push('nofollow');
  if (options.noarchive) directives.push('noarchive');
  if (options.nosnippet) directives.push('nosnippet');
  if (options.noimageindex) directives.push('noimageindex');
  
  // Advanced directives
  if (options.maxSnippet !== undefined) {
    directives.push(`max-snippet:${options.maxSnippet}`);
  }
  if (options.maxImagePreview) {
    directives.push(`max-image-preview:${options.maxImagePreview}`);
  }
  if (options.maxVideoPreview !== undefined) {
    directives.push(`max-video-preview:${options.maxVideoPreview}`);
  }
  
  return directives.length > 0 ? directives.join(', ') : 'index, follow';
}

/**
 * Check for common technical SEO issues
 */
export function checkTechnicalSEOIssues(pageData: {
  url: string;
  title: string;
  description: string;
  headings: Array<{ level: number; text: string }>;
  images: Array<{ src: string; alt?: string }>;
  links: Array<{ href: string; text: string; rel?: string }>;
  canonicalUrl?: string;
  hreflangTags?: Array<{ hreflang: string; href: string }>;
}): Array<{ type: 'error' | 'warning' | 'info'; message: string; fix: string }> {
  const issues: Array<{ type: 'error' | 'warning' | 'info'; message: string; fix: string }> = [];
  
  // URL structure
  if (pageData.url.length > 100) {
    issues.push({
      type: 'warning',
      message: 'URL is too long',
      fix: 'Shorten URL structure and remove unnecessary parameters',
    });
  }
  
  // Canonical URL
  if (!pageData.canonicalUrl) {
    issues.push({
      type: 'warning',
      message: 'Missing canonical URL',
      fix: 'Add canonical link tag to prevent duplicate content issues',
    });
  }
  
  // Hreflang for multilingual sites
  if (!pageData.hreflangTags || pageData.hreflangTags.length === 0) {
    issues.push({
      type: 'info',
      message: 'No hreflang tags found',
      fix: 'Add hreflang tags if site supports multiple languages',
    });
  }
  
  // Internal vs external links
  const internalLinks = pageData.links.filter(link => 
    link.href.startsWith('/') || link.href.includes(seoConfig.baseUrl)
  );
  const externalLinks = pageData.links.filter(link => 
    !link.href.startsWith('/') && !link.href.includes(seoConfig.baseUrl)
  );
  
  if (internalLinks.length < 3) {
    issues.push({
      type: 'warning',
      message: 'Too few internal links',
      fix: 'Add more internal links to improve site navigation and SEO',
    });
  }
  
  // External links without nofollow
  const externalLinksWithoutNofollow = externalLinks.filter(link => 
    !link.rel || !link.rel.includes('nofollow')
  );
  
  if (externalLinksWithoutNofollow.length > 5) {
    issues.push({
      type: 'info',
      message: 'Many external links without nofollow',
      fix: 'Consider adding rel="nofollow" to external links to preserve link equity',
    });
  }
  
  return issues;
}
