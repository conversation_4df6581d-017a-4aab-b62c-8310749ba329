import { SchemaMarkup, BreadcrumbItem, LocalSEO } from './types';
import { seoConfig } from './config';

/**
 * Generate WebSite schema markup
 */
export function generateWebSiteSchema(locale: string = 'zh'): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
    alternateName: 'Bili Tool',
    url: seoConfig.baseUrl,
    description: locale === 'zh' 
      ? '哔哩工具箱提供全面的在线工具集合，包括图像处理、文本编辑、数据转换等多种实用功能。'
      : 'Bili Tool offers a comprehensive collection of online tools, including image processing, text editing, data conversion, and more.',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${seoConfig.baseUrl}/${locale}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${seoConfig.baseUrl}/images/logo.png`,
        width: 512,
        height: 512,
      },
    },
    inLanguage: [
      {
        '@type': 'Language',
        name: 'Chinese',
        alternateName: 'zh',
      },
      {
        '@type': 'Language',
        name: 'English',
        alternateName: 'en',
      },
    ],
  };
}

/**
 * Generate Organization schema markup
 */
export function generateOrganizationSchema(locale: string = 'zh'): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
    alternateName: 'Bili Tool',
    url: seoConfig.baseUrl,
    logo: {
      '@type': 'ImageObject',
      url: `${seoConfig.baseUrl}/images/logo.png`,
      width: 512,
      height: 512,
    },
    description: locale === 'zh'
      ? '专业的在线工具平台，提供图像处理、文本编辑、数据转换等多种实用工具。'
      : 'Professional online tool platform providing image processing, text editing, data conversion and various practical tools.',
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: ['Chinese', 'English'],
    },
    sameAs: [
      'https://github.com/wenhaofree/toolbox-web',
      // Add other social media profiles
    ],
  };
}

/**
 * Generate WebApplication schema for tools
 */
export function generateToolSchema(
  toolId: string,
  toolName: string,
  description: string,
  category: string,
  locale: string = 'zh'
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: toolName,
    url: `${seoConfig.baseUrl}/${locale}/tools/${toolId}`,
    description,
    applicationCategory: 'UtilitiesApplication',
    applicationSubCategory: category,
    operatingSystem: 'Web',
    browserRequirements: 'Requires JavaScript. Requires HTML5.',
    permissions: 'none',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    author: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
    },
    publisher: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${seoConfig.baseUrl}/images/logo.png`,
        width: 512,
        height: 512,
      },
    },
    inLanguage: locale,
    isAccessibleForFree: true,
    usageInfo: `${seoConfig.baseUrl}/${locale}/help`,
    screenshot: {
      '@type': 'ImageObject',
      url: `${seoConfig.baseUrl}/images/tools/${toolId}-screenshot.jpg`,
      caption: `${toolName} screenshot`,
    },
  };
}

/**
 * Generate Article schema for blog posts
 */
export function generateArticleSchema(
  slug: string,
  title: string,
  description: string,
  content: string,
  publishedTime: string,
  modifiedTime: string,
  author: string,
  tags: string[],
  locale: string = 'zh'
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    image: {
      '@type': 'ImageObject',
      url: `${seoConfig.baseUrl}/images/blog/${slug}-featured.jpg`,
      width: 1200,
      height: 630,
    },
    author: {
      '@type': 'Person',
      name: author,
      url: `${seoConfig.baseUrl}/${locale}/author/${author.toLowerCase().replace(/\s+/g, '-')}`,
    },
    publisher: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${seoConfig.baseUrl}/images/logo.png`,
        width: 512,
        height: 512,
      },
    },
    datePublished: publishedTime,
    dateModified: modifiedTime,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${seoConfig.baseUrl}/${locale}/blog/${slug}`,
    },
    articleSection: 'Technology',
    keywords: tags.join(', '),
    wordCount: content.split(/\s+/).length,
    inLanguage: locale,
    url: `${seoConfig.baseUrl}/${locale}/blog/${slug}`,
  };
}

/**
 * Generate BreadcrumbList schema
 */
export function generateBreadcrumbSchema(breadcrumbs: BreadcrumbItem[]): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

/**
 * Generate FAQ schema
 */
export function generateFAQSchema(
  faqs: Array<{ question: string; answer: string }>
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

/**
 * Generate HowTo schema for tutorials
 */
export function generateHowToSchema(
  name: string,
  description: string,
  steps: Array<{ name: string; text: string; image?: string }>,
  totalTime?: string,
  locale: string = 'zh'
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name,
    description,
    totalTime,
    supply: [],
    tool: [],
    step: steps.map((step, index) => ({
      '@type': 'HowToStep',
      position: index + 1,
      name: step.name,
      text: step.text,
      image: step.image ? {
        '@type': 'ImageObject',
        url: step.image,
      } : undefined,
    })),
    author: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
    },
  };
}

/**
 * Generate LocalBusiness schema (if applicable)
 */
export function generateLocalBusinessSchema(
  businessData: LocalSEO,
  locale: string = 'zh'
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: businessData.businessName || (locale === 'zh' ? '哔哩工具箱' : 'Bili Tool'),
    description: locale === 'zh'
      ? '专业的在线工具平台，提供各种实用的网络工具。'
      : 'Professional online tool platform providing various practical web tools.',
    url: seoConfig.baseUrl,
    telephone: businessData.phone,
    address: businessData.address ? {
      '@type': 'PostalAddress',
      streetAddress: businessData.address,
    } : undefined,
    geo: businessData.coordinates ? {
      '@type': 'GeoCoordinates',
      latitude: businessData.coordinates.latitude,
      longitude: businessData.coordinates.longitude,
    } : undefined,
    openingHours: businessData.businessHours ? 
      Object.entries(businessData.businessHours).map(([day, hours]) => `${day} ${hours}`) : 
      undefined,
    priceRange: 'Free',
    currenciesAccepted: 'USD',
    paymentAccepted: 'Free',
  };
}

/**
 * Generate Product schema for premium tools
 */
export function generateProductSchema(
  productName: string,
  description: string,
  price: number,
  currency: string = 'USD',
  locale: string = 'zh'
): SchemaMarkup {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: productName,
    description,
    brand: {
      '@type': 'Brand',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
    },
    manufacturer: {
      '@type': 'Organization',
      name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
      url: seoConfig.baseUrl,
    },
    offers: {
      '@type': 'Offer',
      price,
      priceCurrency: currency,
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: locale === 'zh' ? '哔哩工具箱' : 'Bili Tool',
        url: seoConfig.baseUrl,
      },
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
  };
}
