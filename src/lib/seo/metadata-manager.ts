import { Metadata } from 'next';
import { 
  generateSEOMetadata, 
  generateToolMetadata, 
  generateBlogMetadata,
  generateCategoryMetadata 
} from './metadata';
import { tools } from '@/lib/tools/data';
import { SEOMetadata } from './types';

/**
 * Centralized metadata manager for all pages
 */
export class MetadataManager {
  private static instance: MetadataManager;
  private metadataCache: Map<string, Metadata> = new Map();

  private constructor() {}

  public static getInstance(): MetadataManager {
    if (!MetadataManager.instance) {
      MetadataManager.instance = new MetadataManager();
    }
    return MetadataManager.instance;
  }

  /**
   * Get metadata for home page
   */
  public getHomeMetadata(locale: string = 'zh'): Metadata {
    const cacheKey = `home-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const metadata = generateSEOMetadata({}, locale);
    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get metadata for tool pages
   */
  public getToolMetadata(toolId: string, locale: string = 'zh'): Metadata {
    const cacheKey = `tool-${toolId}-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const tool = tools.find(t => t.id === toolId);
    if (!tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    const toolName = locale === 'zh' ? tool.zh_name || tool.name : tool.en_name || tool.name;
    const toolDescription = locale === 'zh' ? tool.zh_description || tool.description : tool.en_description || tool.description;

    const metadata = generateToolMetadata(toolId, toolName, toolDescription, locale);
    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get metadata for tools index page
   */
  public getToolsIndexMetadata(locale: string = 'zh'): Metadata {
    const cacheKey = `tools-index-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const title = locale === 'zh' 
      ? '在线工具集合 | 哔哩工具箱 - 免费实用工具'
      : 'Online Tools Collection | Bili Tool - Free Practical Tools';
      
    const description = locale === 'zh'
      ? '哔哩工具箱提供丰富的在线工具集合，包括图像处理、文本编辑、数据转换、加密解密等多种实用功能，全部免费使用。'
      : 'Bili Tool offers a rich collection of online tools including image processing, text editing, data conversion, encryption/decryption and more practical functions, all free to use.';

    const metadata = generateSEOMetadata({
      title,
      description,
      canonical: `/${locale}/tools`,
    }, locale);

    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get metadata for category pages
   */
  public getCategoryMetadata(category: string, locale: string = 'zh'): Metadata {
    const cacheKey = `category-${category}-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const categoryTools = tools.filter(tool => tool.category === category);
    const categoryName = category; // You might want to localize this
    
    const description = locale === 'zh'
      ? `${categoryName}相关的在线工具集合，包含${categoryTools.length}个实用工具，助您提高工作效率。`
      : `Collection of ${categoryName} related online tools, including ${categoryTools.length} practical tools to improve your work efficiency.`;

    const metadata = generateCategoryMetadata(category, categoryName, description, locale);
    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get metadata for blog pages
   */
  public getBlogMetadata(
    slug: string,
    title: string,
    description: string,
    publishedTime: string,
    modifiedTime?: string,
    author?: string,
    tags?: string[],
    locale: string = 'zh'
  ): Metadata {
    const cacheKey = `blog-${slug}-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const metadata = generateBlogMetadata(
      slug,
      title,
      description,
      publishedTime,
      modifiedTime,
      author,
      tags,
      locale
    );

    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get metadata for static pages
   */
  public getStaticPageMetadata(
    page: string,
    customData?: Partial<SEOMetadata>,
    locale: string = 'zh'
  ): Metadata {
    const cacheKey = `static-${page}-${locale}`;
    
    if (this.metadataCache.has(cacheKey)) {
      return this.metadataCache.get(cacheKey)!;
    }

    const pageMetadata = this.getStaticPageData(page, locale);
    const metadata = generateSEOMetadata({
      ...pageMetadata,
      ...customData,
    }, locale);

    this.metadataCache.set(cacheKey, metadata);
    return metadata;
  }

  /**
   * Get static page data
   */
  private getStaticPageData(page: string, locale: string): Partial<SEOMetadata> {
    const pageData: Record<string, Record<string, Partial<SEOMetadata>>> = {
      about: {
        zh: {
          title: '关于我们 | 哔哩工具箱 - 专业的在线工具平台',
          description: '了解哔哩工具箱的发展历程、团队介绍和服务理念。我们致力于为用户提供最优质的在线工具服务。',
          canonical: '/zh/about',
        },
        en: {
          title: 'About Us | Bili Tool - Professional Online Tool Platform',
          description: 'Learn about Bili Tool\'s development history, team introduction and service philosophy. We are committed to providing users with the highest quality online tool services.',
          canonical: '/en/about',
        },
      },
      contact: {
        zh: {
          title: '联系我们 | 哔哩工具箱 - 意见反馈与合作咨询',
          description: '联系哔哩工具箱团队，获取技术支持、提交意见反馈或商务合作咨询。我们期待与您的交流。',
          canonical: '/zh/contact',
        },
        en: {
          title: 'Contact Us | Bili Tool - Feedback and Business Inquiries',
          description: 'Contact the Bili Tool team for technical support, feedback submission or business cooperation inquiries. We look forward to communicating with you.',
          canonical: '/en/contact',
        },
      },
      help: {
        zh: {
          title: '帮助中心 | 哔哩工具箱 - 使用指南与常见问题',
          description: '哔哩工具箱帮助中心，提供详细的使用指南、常见问题解答和技术支持，帮助您更好地使用我们的工具。',
          canonical: '/zh/help',
        },
        en: {
          title: 'Help Center | Bili Tool - User Guide and FAQ',
          description: 'Bili Tool Help Center provides detailed user guides, frequently asked questions and technical support to help you better use our tools.',
          canonical: '/en/help',
        },
      },
      privacy: {
        zh: {
          title: '隐私政策 | 哔哩工具箱 - 用户隐私保护',
          description: '哔哩工具箱隐私政策，详细说明我们如何收集、使用和保护您的个人信息，确保用户隐私安全。',
          canonical: '/zh/privacy',
        },
        en: {
          title: 'Privacy Policy | Bili Tool - User Privacy Protection',
          description: 'Bili Tool Privacy Policy details how we collect, use and protect your personal information to ensure user privacy security.',
          canonical: '/en/privacy',
        },
      },
      terms: {
        zh: {
          title: '服务条款 | 哔哩工具箱 - 使用协议',
          description: '哔哩工具箱服务条款，规定用户使用我们服务时的权利和义务，请仔细阅读并遵守相关条款。',
          canonical: '/zh/terms',
        },
        en: {
          title: 'Terms of Service | Bili Tool - User Agreement',
          description: 'Bili Tool Terms of Service specify the rights and obligations when users use our services. Please read and comply with the relevant terms carefully.',
          canonical: '/en/terms',
        },
      },
    };

    return pageData[page]?.[locale] || {};
  }

  /**
   * Clear metadata cache
   */
  public clearCache(): void {
    this.metadataCache.clear();
  }

  /**
   * Clear specific cache entry
   */
  public clearCacheEntry(key: string): void {
    this.metadataCache.delete(key);
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.metadataCache.size,
      keys: Array.from(this.metadataCache.keys()),
    };
  }

  /**
   * Preload metadata for common pages
   */
  public async preloadCommonMetadata(): Promise<void> {
    const locales = ['zh', 'en'];
    const commonPages = ['about', 'contact', 'help', 'privacy', 'terms'];
    
    // Preload home page metadata
    locales.forEach(locale => {
      this.getHomeMetadata(locale);
      this.getToolsIndexMetadata(locale);
    });

    // Preload static pages metadata
    commonPages.forEach(page => {
      locales.forEach(locale => {
        this.getStaticPageMetadata(page, {}, locale);
      });
    });

    // Preload popular tools metadata
    const popularTools = tools.filter(tool => tool.isFavorite).slice(0, 10);
    popularTools.forEach(tool => {
      locales.forEach(locale => {
        this.getToolMetadata(tool.id, locale);
      });
    });
  }
}

// Export singleton instance
export const metadataManager = MetadataManager.getInstance();
