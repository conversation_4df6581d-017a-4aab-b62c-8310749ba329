// SEO Library Main Export File

// Types
export type {
  SEOMetadata,
  OpenGraphData,
  TwitterCardData,
  SchemaMarkup,
  BreadcrumbItem,
  SEOAnalysis,
  SEOIssue,
  SEORecommendation,
  SEOMetrics,
  KeywordAnalysis,
  URLAnalysis,
  ContentAnalysis,
  HeadingAnalysis,
  ImageSEOAnalysis,
  SitemapEntry,
  RobotsDirective,
  SEOConfig,
  PerformanceMetrics,
  MobileOptimization,
  LocalSEO,
} from './types';

// Configuration
export {
  seoConfig,
  localizedSEOConfig,
  SEO_LIMITS,
  SCHEMA_TYPES,
  SITEMAP_PRIORITIES,
  CHANGE_FREQUENCIES,
  DEFAULT_ROBOTS,
  SEO_SCORING_WEIGHTS,
  KEYWORD_ANALYSIS,
  PERFORMANCE_THRESHOLDS,
  IMAGE_SEO,
} from './config';

// Metadata Generation
export {
  generateSEOMetadata,
  generateOpenGraphData,
  generateTwitterCardData,
  generateToolMetadata,
  generateBlogMetadata,
  generateCategoryMetadata,
  validateMetadata,
} from './metadata';

// Schema Markup
export {
  generateWebSiteSchema,
  generateOrganizationSchema,
  generateToolSchema,
  generateArticleSchema,
  generateBreadcrumbSchema,
  generateFAQSchema,
  generateHowToSchema,
  generateLocalBusinessSchema,
  generateProductSchema,
} from './schema';

// SEO Analysis
export {
  analyzePage,
  calculateSEOMetrics,
  analyzeKeywordDensity,
  analyzeHeadingStructure,
  analyzeImageSEO,
  findSEOIssues,
  generateRecommendations,
  calculateSEOScore,
  analyzeKeyword,
  analyzeURL,
} from './analysis';

// Utilities
export {
  generateSlug,
  generateCanonicalURL,
  extractKeywords,
  isStopWord,
  calculateReadingTime,
  generateMetaDescription,
  validateURL,
  generateBreadcrumbs,
  optimizeImageAlt,
  generateRobotsContent,
  extractTextFromHTML,
  calculateFreshnessScore,
  generateHreflangAttributes,
  validateSchemaMarkup,
  generateSocialSharingURLs,
} from './utils';

// Re-export everything for convenience
export * from './types';
export * from './config';
export * from './metadata';
export * from './schema';
export * from './analysis';
export * from './utils';
